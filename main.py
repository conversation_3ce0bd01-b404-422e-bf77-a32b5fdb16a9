#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多站点产品抓取器主程序
根据站点选择调用对应的站点主程序
"""

import subprocess
import sys
from pathlib import Path

# 支持的站点配置
SITES = {
    "walmart": {
        "name": "Walmart",
        "script": "walmart_scraper/walmart_scraper_main.py",
        "description": "美国最大的零售商，产品种类丰富"
    },
    # 未来可以添加更多站点
    # "amazon": {
    #     "name": "Amazon",
    #     "script": "amazon_scraper/main.py",
    #     "description": "全球最大的电商平台"
    # },
    # "target": {
    #     "name": "Target",
    #     "script": "target_scraper/main.py",
    #     "description": "美国知名零售商"
    # }
}

def show_help():
    """显示帮助信息"""
    print("🛒 多站点产品抓取器")
    print("=" * 50)
    print("根据站点选择调用对应的站点主程序")
    print()
    print("使用方法:")
    print("  python main.py [站点名] [站点参数...]")
    print()
    print("示例:")
    print("  python main.py                    # 默认运行 Walmart")
    print("  python main.py walmart            # 明确指定 Walmart 站点")
    print("  python main.py --list             # 列出支持的站点")
    print("  python main.py --help             # 显示此帮助信息")
    print()
    print("站点参数会直接传递给对应的站点主程序")

def list_sites():
    """列出支持的站点"""
    print("🌐 支持的电商站点:")
    print("-" * 50)

    for site_key, site_info in SITES.items():
        print(f"   {site_key:10} - {site_info['name']}")
        print(f"   {'':10}   {site_info['description']}")
        print(f"   {'':10}   程序: {site_info['script']}")
        print()

def main():
    """主函数"""
    # 处理特殊参数
    if len(sys.argv) == 1:
        # 无参数，默认运行 Walmart
        site = "walmart"
        site_args = []
    elif sys.argv[1] in ["--help", "-h"]:
        show_help()
        return
    elif sys.argv[1] == "--list":
        list_sites()
        return
    else:
        # 第一个参数是站点名
        site = sys.argv[1]
        site_args = sys.argv[2:]  # 剩余参数传递给站点程序

    # 验证站点
    if site not in SITES:
        print(f"❌ 不支持的站点: {site}")
        print()
        print("支持的站点:")
        for key, info in SITES.items():
            print(f"  {key}: {info['name']}")
        print()
        print("使用 'python main.py --help' 查看帮助")
        return

    # 获取站点信息
    site_info = SITES[site]
    script_path = Path(site_info["script"])

    # 检查站点程序是否存在
    if not script_path.exists():
        print(f"❌ 站点程序不存在: {script_path}")
        print(f"   请确保 {site_info['name']} 抓取器已正确安装")
        return

    # 启动站点程序
    print(f"🚀 启动 {site_info['name']} 抓取器...")
    print(f"   程序: {script_path}")
    if site_args:
        print(f"   参数: {' '.join(site_args)}")
    print("-" * 50)

    try:
        # 调用站点主程序，传递所有参数
        args = [sys.executable, str(script_path)] + site_args
        result = subprocess.run(args)

        # 返回站点程序的退出码
        sys.exit(result.returncode)

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 启动站点程序失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()