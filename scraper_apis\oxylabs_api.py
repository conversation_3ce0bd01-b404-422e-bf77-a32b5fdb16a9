#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oxylabs Web Scraper API 实现
"""

import requests
from typing import Dict, Any, List
from .base_api import BaseScraperAPI

class OxylabsAPI(BaseScraperAPI):
    """Oxylabs API 实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.endpoint = "https://realtime.oxylabs.io/v1/queries"
        self.session = requests.Session()
    
    def scrape(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Oxylabs 基础抓取

        Args:
            url: 目标URL
            **kwargs: Oxylabs特定参数

        Returns:
            抓取结果
        """
        # 构建基础payload
        payload = {
            "source": kwargs.get("source", "universal"),
            "url": url,
            "render": kwargs.get("render", "html")
        }

        # 处理地理位置参数
        if "geo_location" in kwargs:
            payload["geo_location"] = kwargs["geo_location"]
        elif "delivery_zip" in kwargs:
            payload["delivery_zip"] = kwargs["delivery_zip"]
        else:
            payload["geo_location"] = "United States"

        # 添加浏览器指令（如果有）
        if "browser_instructions" in kwargs:
            payload["browser_instructions"] = kwargs["browser_instructions"]



        # 添加其他参数（排除已处理的参数）
        excluded_keys = {"source", "render", "geo_location", "delivery_zip", "browser_instructions"}
        for key, value in kwargs.items():
            if key not in excluded_keys:
                payload[key] = value

        # 打印所有payload参数用于调试
        print(f"🔍 scrape 最终请求载荷:")
        for key, value in payload.items():
            if key == "parsing_instructions" and isinstance(value, dict):
                print(f"   {key}: {len(value)} 个解析指令")
            elif key == "browser_instructions" and isinstance(value, list):
                print(f"   {key}: {len(value)} 条浏览器指令")
            else:
                print(f"   {key}: {value}")

        return self._make_request(payload)
    
    def scrape_with_parsing(self, url: str, parsing_instructions: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Oxylabs 带解析的抓取

        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            **kwargs: Oxylabs特定参数

        Returns:
            抓取和解析结果
        """
        # 构建基础payload，确保必要参数存在
        payload = {
            "source": kwargs.get("source", "universal_ecommerce"),
            "url": url,
            "render": kwargs.get("render", "html")
        }

        # 处理地理位置参数
        if "geo_location" in kwargs:
            payload["geo_location"] = kwargs["geo_location"]
        elif "delivery_zip" in kwargs:
            payload["delivery_zip"] = kwargs["delivery_zip"]
        else:
            payload["geo_location"] = "United States"

        # 只有当解析指令不为空时才添加解析参数
        if parsing_instructions:
            payload["parse"] = True
            payload["parsing_instructions"] = parsing_instructions

        # 添加浏览器指令（如果有）
        if "browser_instructions" in kwargs:
            payload["browser_instructions"] = kwargs["browser_instructions"]

        # 添加其他参数（排除已处理的参数）
        excluded_keys = {"source", "render", "geo_location", "delivery_zip", "browser_instructions"}
        for key, value in kwargs.items():
            if key not in excluded_keys:
                payload[key] = value

        # 打印所有payload参数用于调试
        print(f"🔍 scrape_with_parsing 最终请求载荷:")
        for key, value in payload.items():
            if key == "parsing_instructions" and isinstance(value, dict):
                print(f"   {key}: {len(value)} 个解析指令")
            elif key == "browser_instructions" and isinstance(value, list):
                print(f"   {key}: {len(value)} 条浏览器指令")
            else:
                print(f"   {key}: {value}")

        return self._make_request(payload)

    def scrape_with_browser_instructions(self, url: str, browser_instructions: List[Dict[str, Any]],
                                       parsing_instructions: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """
        使用Browser Instructions抓取页面

        Args:
            url: 目标URL
            browser_instructions: 浏览器指令列表
            parsing_instructions: 解析指令（可选）
            **kwargs: 其他参数

        Returns:
            抓取结果
        """
        # 构建基础payload
        payload = {
            "source": kwargs.get("source", "universal_ecommerce"),
            "url": url,
            "render": "html",
            "browser_instructions": browser_instructions
        }

        # 处理地理位置参数
        if "geo_location" in kwargs:
            payload["geo_location"] = kwargs["geo_location"]
        elif "delivery_zip" in kwargs:
            payload["delivery_zip"] = kwargs["delivery_zip"]
        else:
            payload["geo_location"] = "United States"

        # 如果提供了解析指令，添加到payload中
        if parsing_instructions:
            payload["parse"] = True
            payload["parsing_instructions"] = parsing_instructions

        # 添加其他参数（排除已处理的参数）
        excluded_keys = {"source", "render", "geo_location", "delivery_zip", "browser_instructions"}
        for key, value in kwargs.items():
            if key not in excluded_keys:
                payload[key] = value

        # 打印所有payload参数用于调试
        print(f"🔍 scrape_with_browser_instructions 最终请求载荷:")
        for key, value in payload.items():
            if key == "parsing_instructions" and isinstance(value, dict):
                print(f"   {key}: {len(value)} 个解析指令")
            elif key == "browser_instructions" and isinstance(value, list):
                print(f"   {key}: {len(value)} 条浏览器指令")
            else:
                print(f"   {key}: {value}")

        return self._make_request(payload)

    def get_supported_features(self) -> Dict[str, bool]:
        """获取 Oxylabs 支持的功能"""
        return {
            "javascript_rendering": True,
            "custom_parsing": True,
            "browser_instructions": True,
            "geo_location": True,
            "proxy_rotation": True,
            "session_management": True,
            "captcha_solving": True
        }
    
    def transform_strategy(self, strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将通用策略转换为 Oxylabs 配置
        
        Args:
            strategy_config: 通用策略配置
            
        Returns:
            Oxylabs 特定配置
        """
        oxylabs_config = {}
        
        # 转换浏览器指令
        if "browser_instructions" in strategy_config:
            oxylabs_config["browser_instructions"] = strategy_config["browser_instructions"]
        
        # 转换渲染设置
        if "render" in strategy_config:
            oxylabs_config["render"] = strategy_config["render"]
        else:
            oxylabs_config["render"] = "html"
        
        # 转换地理位置
        if "geo_location" in strategy_config:
            oxylabs_config["geo_location"] = strategy_config["geo_location"]
        else:
            oxylabs_config["geo_location"] = "United States"
        
        # 转换源类型
        if "source" in strategy_config:
            oxylabs_config["source"] = strategy_config["source"]
        else:
            oxylabs_config["source"] = "universal_ecommerce"
        
        return oxylabs_config
    
    def get_required_config_fields(self) -> List[str]:
        """获取 Oxylabs 必需的配置字段"""
        return ["username", "password"]
    
    def _make_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送请求到 Oxylabs API

        Args:
            payload: 请求载荷

        Returns:
            API 响应
        """
        try:
            # 清晰显示发送给Oxylabs的确切URL
            target_url = payload.get('url', 'Unknown')
            print(f"🎯 发送给Oxylabs的目标URL: {target_url}")
            # 自动打印所有payload参数
            print(f"📦 Oxylabs请求载荷:")
            for key, value in payload.items():
                if key == "parsing_instructions" and isinstance(value, dict):
                    print(f"   {key}: {len(value)} 个解析指令")
                elif key == "browser_instructions" and isinstance(value, list):
                    print(f"   {key}: {len(value)} 条浏览器指令")
                else:
                    print(f"   {key}: {value}")

            response = self.session.post(
                self.endpoint,
                auth=(self.config["username"], self.config["password"]),
                json=payload,
                timeout=120
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Oxylabs API 请求失败: {e}")
            return {}
    
    def get_cost_estimate(self, requests_count: int) -> Dict[str, Any]:
        """
        Oxylabs 成本估算
        
        Args:
            requests_count: 请求数量
            
        Returns:
            成本估算
        """
        # Oxylabs 大概每1000次请求 $15-50（根据套餐不同）
        cost_per_1k = 25  # 平均值
        estimated_cost = (requests_count / 1000) * cost_per_1k
        
        return {
            "requests": requests_count,
            "estimated_cost": f"${estimated_cost:.2f}",
            "currency": "USD",
            "cost_per_1k_requests": f"${cost_per_1k}",
            "note": "基于平均定价估算，实际费用可能因套餐而异"
        }
