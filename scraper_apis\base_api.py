#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Scraper API 抽象基类
定义所有 API 实现必须遵循的接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

class BaseScraperAPI(ABC):
    """Web Scraper API 抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 API
        
        Args:
            config: API 配置字典
        """
        self.config = config
        self.name = self.__class__.__name__.replace("API", "").lower()
    
    @abstractmethod
    def scrape(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        抓取页面基础方法
        
        Args:
            url: 目标URL
            **kwargs: API特定参数
            
        Returns:
            抓取结果字典
        """
        pass
    
    @abstractmethod
    def scrape_with_parsing(self, url: str, parsing_instructions: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        带自定义解析的抓取方法
        
        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            **kwargs: API特定参数
            
        Returns:
            抓取和解析结果
        """
        pass
    
    @abstractmethod
    def get_supported_features(self) -> Dict[str, bool]:
        """
        获取 API 支持的功能列表
        
        Returns:
            功能支持字典
        """
        pass
    
    @abstractmethod
    def transform_strategy(self, strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将通用策略配置转换为 API 特定配置
        
        Args:
            strategy_config: 通用策略配置
            
        Returns:
            API 特定配置
        """
        pass
    
    def validate_config(self) -> bool:
        """
        验证 API 配置是否有效
        
        Returns:
            配置是否有效
        """
        required_fields = self.get_required_config_fields()
        for field in required_fields:
            if field not in self.config:
                print(f"❌ 缺少必需配置: {field}")
                return False
        return True
    
    @abstractmethod
    def get_required_config_fields(self) -> List[str]:
        """
        获取必需的配置字段列表
        
        Returns:
            必需字段列表
        """
        pass
    
    def get_api_info(self) -> Dict[str, Any]:
        """
        获取 API 基本信息
        
        Returns:
            API 信息字典
        """
        return {
            "name": self.name,
            "features": self.get_supported_features(),
            "config_valid": self.validate_config()
        }
    
    def test_connection(self) -> bool:
        """
        测试 API 连接

        Returns:
            连接是否成功
        """
        try:
            # 使用一个简单的测试URL
            test_url = "https://httpbin.org/html"
            result = self.scrape(test_url)
            return bool(result and 'content' in result)
        except Exception as e:
            print(f"❌ API 连接测试失败: {e}")
            return False

    def get_cost_estimate(self, requests_count: int) -> Dict[str, Any]:
        """
        估算抓取成本（子类可重写）

        Args:
            requests_count: 请求数量

        Returns:
            成本估算信息
        """
        return {
            "requests": requests_count,
            "estimated_cost": "未知",
            "currency": "USD",
            "note": f"{self.name} API 成本估算未实现"
        }
