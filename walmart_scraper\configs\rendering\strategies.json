{"_description": "渲染策略配置文件", "_version": "3.0.0", "_last_updated": "2025-07-05", "strategies": {"fast": {"_description": "快速渲染策略 - 最小等待时间", "render": "html", "timeout_s": 15, "browser_instructions": [{"type": "wait", "wait_time_s": 2}]}, "standard": {"_description": "标准渲染策略 - 平衡性能和稳定性（产品页面）", "render": "html", "timeout_s": 30, "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 10}, {"type": "scroll", "x": 0, "y": 2000}]}, "category_discovery": {"_description": "分类发现页面专用策略", "render": "html", "timeout_s": 35, "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "#Hubspokes4orNxMGrid"}, "timeout_s": 15}, {"type": "wait_for_element", "selector": {"type": "css", "value": "#Hubspokes4orNxMGrid div[role='listitem']"}, "timeout_s": 10}, {"type": "scroll", "x": 0, "y": 1500}, {"type": "wait", "wait_time_s": 5}]}, "enhanced": {"_description": "增强渲染策略 - 最大兼容性", "render": "html", "timeout_s": 45, "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 15}, {"type": "scroll", "x": 0, "y": 3000}, {"type": "wait", "wait_time_s": 5}]}, "ecommerce": {"_description": "电商专用策略 - 针对产品页面优化", "render": "html", "timeout_s": 45, "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 15}, {"type": "wait", "wait_time_s": 5}, {"type": "scroll", "x": 0, "y": 2500}, {"type": "wait", "wait_time_s": 3}, {"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack'] div[role='group'][data-item-id]"}, "timeout_s": 10}, {"type": "wait", "wait_time_s": 2}]}, "product_list": {"_description": "产品列表专用策略 - 确保排序和产品完全加载", "render": "html", "timeout_s": 50, "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 15}, {"type": "wait", "wait_time_s": 8}, {"type": "scroll", "x": 0, "y": 3000}, {"type": "wait", "wait_time_s": 5}, {"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack'] div[role='group'][data-item-id]"}, "timeout_s": 12}, {"type": "wait", "wait_time_s": 3}]}}, "fallback_strategy": "standard", "_fallback_description": "当指定策略失败时使用的备用策略", "common_selectors": {"_description": "通用选择器配置", "product_container": "div[data-testid='item-stack']", "pagination": "nav[aria-label='pagination']", "loading_indicator": ".loading, .spinner, [data-testid='loading']"}, "performance": {"_description": "性能配置", "max_wait_time": 60, "scroll_delay": 1, "element_wait_timeout": 15}}