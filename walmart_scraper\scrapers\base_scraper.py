#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础抓取器
提供所有抓取器的通用功能和接口
"""

import json
import time
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..utils.logger import get_logger, get_logger_manager
from ..workflow.workflow_config import WorkflowConfig


class BaseScraper(ABC):
    """基础抓取器抽象类"""
    
    def __init__(self, config: WorkflowConfig, scraper_name: str = "base"):
        """
        初始化基础抓取器
        
        Args:
            config: 工作流配置
            scraper_name: 抓取器名称
        """
        self.config = config
        self.scraper_name = scraper_name
        self.logger = get_logger(f"walmart_scraper.scrapers.{scraper_name}")
        self.logger_manager = get_logger_manager()
        
        # 加载抓取器配置
        self.scraper_config = self._load_scraper_config()
        
        # 初始化HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Oxylabs API配置
        self.oxylabs_config = self.scraper_config.get('oxylabs', {})
        self.api_url = self.oxylabs_config.get('api_url')
        self.username = self.oxylabs_config.get('username')
        self.password = self.oxylabs_config.get('password')
        
        self.logger.debug(f"🔧 {scraper_name} 抓取器初始化完成")
    
    def _load_scraper_config(self) -> Dict[str, Any]:
        """加载抓取器配置"""
        try:
            import os
            from pathlib import Path
            
            config_path = Path(__file__).parent.parent / "configs" / "scraper_config.json"
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 过滤注释字段
            return self._filter_comments(config_data)
            
        except Exception as e:
            self.logger.warning(f"⚠️  抓取器配置加载失败，使用默认配置: {e}")
            return self._get_default_scraper_config()
    
    def _filter_comments(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤配置中的注释字段（以_开头但不是解析指令的字段）"""
        if isinstance(config, dict):
            # 特殊处理：保留解析指令中的 _fns, _items, _fn, _args 等字段
            parsing_instruction_fields = {'_fns', '_items', '_fn', '_args'}

            return {
                key: self._filter_comments(value)
                for key, value in config.items()
                if not key.startswith('_') or key in parsing_instruction_fields
            }
        elif isinstance(config, list):
            return [self._filter_comments(item) for item in config]
        else:
            return config
    
    def _get_default_scraper_config(self) -> Dict[str, Any]:
        """获取默认抓取器配置"""
        return {
            "oxylabs": {
                "username": "",
                "password": "",
                "api_url": "https://realtime.oxylabs.io/v1/queries",
                "default_params": {
                    "source": "universal_ecommerce",
                    "geo_location": "United States",
                    "locale": "en-US",
                    "render": "html"
                }
            }
        }
    
    def make_request(self, url: str, parsing_instructions: Dict[str, Any] = None,
                    strategy: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        发送Oxylabs API请求
        
        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            strategy: 渲染策略
            **kwargs: 额外参数
            
        Returns:
            API响应结果
        """
        start_time = time.time()
        
        try:
            # 构建请求载荷
            payload = self._build_request_payload(url, parsing_instructions, strategy, **kwargs)
            
            # 记录请求信息
            self.logger_manager.log_request_info(
                self.logger, url, "POST"
            )

            # 调试：记录请求载荷
            self.logger.debug(f"🔍 API请求载荷: {json.dumps(payload, indent=2, ensure_ascii=False)}")

            # 发送请求
            response = self.session.post(
                self.api_url,
                json=payload,
                auth=(self.username, self.password),
                timeout=self.config.performance.timeout
            )
            
            duration = time.time() - start_time
            
            # 记录性能信息
            self.logger_manager.log_performance(
                self.logger, f"API请求 {self.scraper_name}", duration,
                status_code=response.status_code
            )
            
            response.raise_for_status()
            result = response.json()
            
            self.logger.debug(f"✅ API请求成功: {response.status_code}")
            return result
            
        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            self.logger.error(f"❌ API请求失败: {e}")

            # 记录错误响应详情
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.text
                    self.logger.error(f"❌ 错误响应内容: {error_detail}")
                except:
                    pass

            # 记录失败的请求
            self.logger_manager.log_performance(
                self.logger, f"API请求失败 {self.scraper_name}", duration,
                error=str(e)
            )

            return None
        except Exception as e:
            self.logger.error(f"❌ 请求处理异常: {e}")
            return None
    
    def _build_request_payload(self, url: str, parsing_instructions: Dict[str, Any] = None,
                              strategy: str = None, **kwargs) -> Dict[str, Any]:
        """
        构建请求载荷
        
        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            strategy: 渲染策略
            **kwargs: 额外参数
            
        Returns:
            请求载荷
        """
        # 基础参数
        payload = {
            "url": url,
            **self.oxylabs_config.get('default_params', {})
        }
        
        # 只有当解析指令不为空时才添加解析参数
        if parsing_instructions:
            payload["parse"] = True
            payload["parsing_instructions"] = parsing_instructions
        
        # 添加渲染策略
        if strategy:
            strategy_config = self._get_strategy_config(strategy)
            if strategy_config:
                payload.update(strategy_config)
        
        # 添加额外参数
        payload.update(kwargs)
        
        return payload
    
    def _get_strategy_config(self, strategy: str) -> Dict[str, Any]:
        """
        获取渲染策略配置
        
        Args:
            strategy: 策略名称
            
        Returns:
            策略配置
        """
        strategies = self.scraper_config.get('rendering_strategies', {}).get('strategies', {})
        strategy_config = strategies.get(strategy, {})
        
        if 'browser_instructions' in strategy_config:
            return {"browser_instructions": strategy_config['browser_instructions']}
        
        return {}
    
    def retry_request(self, request_func, max_retries: int = None, 
                     retry_delay: int = None, *args, **kwargs) -> Optional[Any]:
        """
        带重试的请求执行
        
        Args:
            request_func: 请求函数
            max_retries: 最大重试次数
            retry_delay: 重试延迟
            *args, **kwargs: 请求函数参数
            
        Returns:
            请求结果
        """
        if max_retries is None:
            max_retries = self.config.performance.retry_attempts
        if retry_delay is None:
            retry_delay = self.config.performance.retry_delay
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = request_func(*args, **kwargs)
                if result:  # 成功获取结果
                    if attempt > 0:
                        self.logger.info(f"✅ 重试成功: 第 {attempt + 1} 次尝试")
                    return result
                    
            except Exception as e:
                last_exception = e
                self.logger.warning(f"⚠️  请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries:
                self.logger.info(f"⏱️  等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
        
        self.logger.error(f"❌ 所有重试均失败，最后错误: {last_exception}")
        return None
    
    def extract_data_from_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        从API响应中提取数据
        
        Args:
            response: API响应
            
        Returns:
            提取的数据
        """
        if not response or 'results' not in response:
            self.logger.warning("⚠️  响应中无有效数据")
            return {}
        
        try:
            content = response['results'][0].get('content', {})
            
            # 如果有解析结果，优先使用
            if isinstance(content, dict) and any(key for key in content.keys() if not key.startswith('_')):
                return content
            
            # 否则返回原始HTML内容
            if isinstance(content, str):
                return {"html": content}
            
            return content
            
        except (IndexError, KeyError, TypeError) as e:
            self.logger.error(f"❌ 数据提取失败: {e}")
            return {}
    
    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        验证API响应的有效性
        
        Args:
            response: API响应
            
        Returns:
            是否有效
        """
        if not response:
            return False
        
        if 'results' not in response:
            self.logger.warning("⚠️  响应缺少results字段")
            return False
        
        results = response['results']
        if not results or len(results) == 0:
            self.logger.warning("⚠️  响应results为空")
            return False
        
        first_result = results[0]
        if 'content' not in first_result:
            self.logger.warning("⚠️  响应缺少content字段")
            return False
        
        return True
    
    def build_full_url(self, relative_url: str, base_url: str = None) -> str:
        """
        构建完整URL
        
        Args:
            relative_url: 相对URL
            base_url: 基础URL
            
        Returns:
            完整URL
        """
        if relative_url.startswith('http'):
            return relative_url
        
        if base_url is None:
            base_url = self.scraper_config.get('walmart', {}).get('base_url', 'https://www.walmart.com')
        
        return f"{base_url.rstrip('/')}/{relative_url.lstrip('/')}"
    
    @abstractmethod
    def scrape(self, *args, **kwargs) -> Dict[str, Any]:
        """
        抽象抓取方法，子类必须实现
        
        Returns:
            抓取结果
        """
        pass


# 便捷函数
def create_base_scraper(config: WorkflowConfig, scraper_name: str) -> BaseScraper:
    """创建基础抓取器的便捷函数（仅用于测试）"""
    class TestScraper(BaseScraper):
        def scrape(self, *args, **kwargs):
            return {"test": "data"}
    
    return TestScraper(config, scraper_name)
