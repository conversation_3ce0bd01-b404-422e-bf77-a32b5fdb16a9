#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页抓取器 - 处理多页产品列表的抓取逻辑
"""

import time
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from walmart_scraper.utils.pagination_utils import PaginationUtils
from walmart_scraper.modules.product_list_module import ProductListModule
from utils.logger.logger import get_logger

class PaginationModule:
    """分页模块"""
    
    def __init__(self, config):
        """
        初始化分页抓取器

        Args:
            config: 配置对象（WorkflowConfig或字典）
        """
        self.config = config
        self.logger = get_logger(self.__class__.__name__)

        # 初始化工具类
        self.pagination_utils = PaginationUtils()
        self.product_list_module = ProductListModule(config)

        # 处理配置对象
        if hasattr(config, 'pagination'):
            # WorkflowConfig对象
            pagination_dict = {
                'max_pages': getattr(config.pagination, 'max_pages_per_category', 5),
                'max_products_per_page': getattr(config.pagination, 'max_products_per_page', 20),
                'use_min_principle': True
            }
        elif isinstance(config, dict):
            # 字典配置
            pagination_dict = config.get('pagination', {})
        else:
            # 默认配置
            pagination_dict = {}

        # 加载配置文件
        self.file_config = self._load_pagination_config()

        # 合并配置（优先级：用户配置 > 文件配置 > 默认配置）
        merged_config = {**self.file_config.get('pagination', {}), **pagination_dict}

        # 验证配置
        self.pagination_config = self.pagination_utils.validate_pagination_config(merged_config)

        self.logger.info(f"📦 分页模块初始化完成")
        self.logger.debug(f"分页配置: {self.pagination_config}")

    def build_page_url(self, base_url: str, page_num: int) -> str:
        """
        构建分页URL

        Args:
            base_url: 基础URL
            page_num: 页码

        Returns:
            构建的分页URL
        """
        return self.pagination_utils.build_walmart_pagination_url(base_url, page_num)

    def detect_max_pages_with_sorting(self, category_url: str, sorting_config: Dict[str, Any] = None) -> int:
        """
        检测分类的最大页数（考虑排序）

        Args:
            category_url: 分类URL
            sorting_config: 排序配置

        Returns:
            最大页数
        """
        try:
            # 使用第一页来检测最大页数
            if sorting_config:
                result = self.product_list_module.scrape_page_with_sorting(category_url, sorting_config)
            else:
                result = self.product_list_module.scrape_page(category_url)

            if result and result.get('success'):
                pagination_info = result.get('pagination_info', {})
                total_pages = pagination_info.get('total_pages')

                if total_pages and isinstance(total_pages, int) and total_pages > 0:
                    self.logger.debug(f"🔍 检测到最大页数: {total_pages}")
                    return total_pages
                else:
                    self.logger.warning(f"⚠️  无法从分页信息中获取有效页数: {pagination_info}")
                    return self.pagination_config.get('max_pages', 5)
            else:
                self.logger.warning(f"⚠️  页数检测请求失败")
                return self.pagination_config.get('max_pages', 5)

        except Exception as e:
            self.logger.error(f"❌ 页数检测失败: {e}")
            return self.pagination_config.get('max_pages', 5)

    def _load_pagination_config(self) -> Dict[str, Any]:
        """加载分页配置文件"""
        try:
            config_path = os.path.join(
                os.path.dirname(__file__),
                '..', 'configs', 'modules', 'pagination.json'
            )

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.debug(f"✅ 加载分页配置文件: {config_path}")
                return config
            else:
                self.logger.warning(f"⚠️  分页配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"❌ 加载分页配置文件失败: {e}")
            return {}

    def scrape_category_with_pagination(self, base_url: str, custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行完整的分页抓取流程

        流程：
        1. 第1页：直接抓取（排序已在URL中处理）
        2. 后续页：使用构建的分页URL（排序状态自动保持）
        3. 收集所有产品URL和标题
        4. 应用最小值原则

        Args:
            base_url: 分类基础URL（已包含排序参数）
            custom_config: 自定义配置（可选）

        Returns:
            抓取结果字典
        """
        start_time = time.time()
        self.logger.info(f"🚀 开始分页抓取: {base_url}")
        
        # 合并配置
        effective_config = {**self.pagination_config}
        if custom_config:
            effective_config.update(custom_config)
        
        self.logger.info(f"📋 抓取配置: 最大{effective_config['max_pages']}页, 每页最多{effective_config['max_products_per_page']}个产品")
        
        all_products = []
        pagination_summary = {}
        actual_data = {}
        
        try:
            for page_num in range(1, effective_config['max_pages'] + 1):
                self.logger.info(f"\n📄 抓取第 {page_num} 页...")
                
                page_start_time = time.time()
                
                if page_num == 1:
                    # 第1页：直接抓取（排序已在URL中处理）
                    page_result = self.product_list_module.scrape_page(base_url)
                    
                    # 从第1页获取实际数据信息
                    if page_result.get('success'):
                        pagination_info = page_result.get('pagination_info', {})
                        actual_data = {
                            'total_pages': pagination_info.get('total_pages'),
                            'products_per_page': len(page_result.get('products', []))
                        }
                        
                        # 应用最小值原则
                        if effective_config.get('use_min_principle', True):
                            effective_config = self.pagination_utils.apply_min_principle(effective_config, actual_data)
                            self.logger.info(f"📊 应用最小值原则后的配置: 最大{effective_config['max_pages']}页, 每页最多{effective_config['max_products_per_page']}个产品")
                else:
                    # 后续页：使用分页URL
                    page_result = self._scrape_subsequent_page(base_url, page_num, effective_config)
                
                page_duration = time.time() - page_start_time
                
                if not page_result.get('success'):
                    self.logger.warning(f"⚠️  第 {page_num} 页抓取失败，停止抓取")
                    break
                
                # 处理页面产品
                page_products = page_result.get('products', [])
                original_count = len(page_products)
                
                # 应用每页产品数限制
                limited_products = page_products[:effective_config['max_products_per_page']]
                limited_count = len(limited_products)
                
                # 为产品添加页面信息
                for i, product in enumerate(limited_products):
                    product['page_number'] = page_num
                    product['position_on_page'] = i + 1
                    product['scraped_at'] = datetime.now().isoformat()
                
                all_products.extend(limited_products)
                
                # 记录页面摘要
                pagination_summary[f'page_{page_num}'] = {
                    'products_found': original_count,
                    'products_collected': limited_count,
                    'has_next_page': bool(page_result.get('pagination_info', {}).get('has_next_page')),
                    'duration_seconds': round(page_duration, 2)
                }
                
                # 详细的页面产品统计
                if original_count != limited_count:
                    self.logger.info(f"📄 第 {page_num} 页完成: 网站返回{original_count}个产品, 配置限制{effective_config['max_products_per_page']}个, 实际收集{limited_count}个, 耗时{page_duration:.2f}秒")
                else:
                    self.logger.info(f"📄 第 {page_num} 页完成: 返回并收集{original_count}个产品, 耗时{page_duration:.2f}秒")
                
                # 检查是否还有下一页
                if not page_result.get('pagination_info', {}).get('has_next_page'):
                    self.logger.info(f"📄 已到达最后一页: {page_num}")
                    break
                
                # 检查是否达到配置的最大页数
                if page_num >= effective_config['max_pages']:
                    self.logger.info(f"📄 已达到配置的最大页数: {effective_config['max_pages']}")
                    break
                
                # 页面间隔（避免请求过于频繁）
                if page_num < effective_config['max_pages']:
                    time.sleep(1)
            
            total_duration = time.time() - start_time
            
            # 构建结果
            result = {
                'success': True,
                'category_url': base_url,
                'sort_type': effective_config['sort_type'],
                'total_pages_scraped': len(pagination_summary),
                'total_products': len(all_products),
                'products': all_products,
                'pagination_summary': pagination_summary,
                'actual_data': actual_data,
                'effective_config': effective_config,
                'execution_time': round(total_duration, 2),
                'scraped_at': datetime.now().isoformat()
            }
            
            self.logger.info(f"🎉 分页抓取完成!")
            self.logger.info(f"   - 总页数: {len(pagination_summary)}")
            self.logger.info(f"   - 总产品数: {len(all_products)}")
            self.logger.info(f"   - 总耗时: {total_duration:.2f}秒")

            # 显示每页详细统计
            self.logger.info(f"📊 各页产品数量统计:")
            for page_key, page_info in pagination_summary.items():
                page_num = page_key.replace('page_', '')
                found = page_info['products_found']
                collected = page_info['products_collected']
                if found != collected:
                    self.logger.info(f"   - 第{page_num}页: 网站返回{found}个, 实际收集{collected}个")
                else:
                    self.logger.info(f"   - 第{page_num}页: {found}个产品")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 分页抓取失败: {e}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'category_url': base_url,
                'products': all_products,
                'pagination_summary': pagination_summary,
                'execution_time': round(time.time() - start_time, 2)
            }
    


    def _scrape_subsequent_page(self, base_url: str, page_num: int, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        抓取后续页面（使用分页URL）
        
        Args:
            base_url: 基础URL
            page_num: 页码
            config: 配置字典
            
        Returns:
            抓取结果
        """
        # 构建分页URL
        page_url = self.pagination_utils.build_walmart_pagination_url(base_url, page_num)
        self.logger.debug(f"🔗 第{page_num}页URL: {page_url}")
        
        # 使用简单抓取（不需要排序，状态已保持）
        return self.product_list_module.scrape_page(page_url)
    
    def save_products_to_json(self, products_data: Dict[str, Any], output_file: str) -> bool:
        """
        保存产品数据到JSON文件
        
        Args:
            products_data: 产品数据字典
            output_file: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            import json
            import os
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 保存数据
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(products_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ 产品数据已保存到: {output_file}")
            self.logger.info(f"   - 文件大小: {os.path.getsize(output_file) / 1024:.1f} KB")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存产品数据失败: {e}")
            return False
