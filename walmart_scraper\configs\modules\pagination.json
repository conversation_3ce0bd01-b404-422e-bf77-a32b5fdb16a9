{"_description": "分页抓取配置 - 新架构", "_version": "3.0.0", "_last_updated": "2025-07-05", "pagination": {"_description": "分页基础配置", "max_pages": 3, "max_products_per_page": 100, "sort_type": "best_seller", "use_min_principle": true, "page_delay_seconds": 1, "max_pages_per_category": 3, "limit_strategy": "min", "auto_detect_max_pages": false, "page_detection_timeout": 30}, "_pagination_extended_description": "从工作流配置迁移的分页业务配置", "url_patterns": {"_description": "Walmart分页URL模式配置", "base_pattern": "{base_url}?{seo_params}&page={page_num}&affinityOverride=default", "seo_param_template": "seo={seo_value}", "seo_separator": "&"}, "parsing_instructions": {"_description": "分页信息解析指令 - 从product_list.json迁移", "_note": "统一分页相关配置到pagination.json中", "pagination_info": {"_description": "分页信息提取 - 优化版本，基于实际HTML结构", "_html_structure_note": "基于nav[aria-label='pagination']结构，当前页有aria-current='page'，最大页数在最后的div中", "current_page": {"_description": "当前页码 - 通过aria-current='page'属性定位", "_fns": [{"_fn": "css_one", "_args": ["nav[aria-label='pagination'] a[aria-current='page'][data-automation-id='page-number']"]}, {"_fn": "element_text"}, {"_fn": "convert_to_int"}]}, "total_pages": {"_description": "最大页数 - 通过最后一个数字div获取（排除...和NextPage）", "_fns": [{"_fn": "css", "_args": ["nav[aria-label='pagination'] li:last-child:not(:has(a[data-testid='NextPage'])) div.sans-serif"]}, {"_fn": "element_text"}, {"_fn": "convert_to_int"}]}, "total_pages_fallback": {"_description": "最大页数备用方案 - 查找所有数字div，取最大值", "_fns": [{"_fn": "css", "_args": ["nav[aria-label='pagination'] div.sans-serif"]}, {"_fn": "element_text"}, {"_fn": "regex_search", "_args": ["^\\d+$"]}, {"_fn": "convert_to_int"}, {"_fn": "max_value"}]}, "total_pages_xpath_fallback": {"_description": "最大页数XPath备用方案 - 更精确的定位", "_fns": [{"_fn": "xpath_one", "_args": "//nav[@aria-label='pagination']//div[@class and contains(@class, 'sans-serif') and not(contains(text(), '...'))][last()]/text()"}, {"_fn": "convert_to_int"}]}, "next_page_exists": {"_description": "下一页链接存在性检查", "_fns": [{"_fn": "css_one", "_args": ["nav[aria-label='pagination'] a[data-testid='NextPage']"]}, {"_fn": "xpath_one", "_args": "./@href"}]}, "has_next_page": {"_description": "是否有下一页 - 检查NextPage按钮是否可点击（href不为#）", "_fns": [{"_fn": "css", "_args": ["nav[aria-label='pagination'] a[data-testid='NextPage']:not([href='#'])"]}, {"_fn": "length"}, {"_fn": "convert_to_int"}]}, "pagination_debug_info": {"_description": "调试信息 - 获取整个分页区域的HTML用于问题排查", "_fns": [{"_fn": "css_one", "_args": ["nav[aria-label='pagination']"]}, {"_fn": "element_html"}]}}}, "rendering_strategies": {"_description": "分页相关的渲染策略", "with_pagination_wait": {"_description": "分页等待策略 - 确保分页区域完全加载", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 15, "_note": "等待产品容器加载"}, {"type": "wait_for_element", "selector": {"type": "css", "value": "nav[aria-label='pagination']"}, "timeout_s": 20, "_note": "等待分页导航加载"}, {"type": "wait_for_element", "selector": {"type": "css", "value": "nav[aria-label='pagination'] a[aria-current='page']"}, "timeout_s": 15, "_note": "等待当前页标识加载"}, {"type": "scroll", "x": 0, "y": 3000, "_note": "滚动确保所有内容加载"}, {"type": "wait", "wait_time_s": 8, "_note": "额外等待确保分页信息稳定"}]}}, "validation": {"_description": "数据验证配置", "min_title_percentage": 90, "min_link_percentage": 90, "required_fields": ["title", "link", "page_number", "position_on_page"]}, "output": {"_description": "输出配置", "save_to_json": true, "output_directory": "output", "filename_template": "collected_product_urls_{timestamp}.json", "include_metadata": true, "include_pagination_summary": true}, "error_handling": {"_description": "错误处理配置", "max_retries": 3, "retry_delay_seconds": 2, "fallback_to_simple_scraping": true, "continue_on_page_error": true}, "performance": {"_description": "性能配置", "concurrent_requests": false, "request_timeout_seconds": 60, "page_load_timeout_seconds": 30, "memory_limit_mb": 512}}