{"_description": "分页抓取配置 - 新架构", "_version": "3.0.0", "_last_updated": "2025-07-05", "pagination": {"_description": "分页基础配置", "max_pages": 3, "max_products_per_page": 100, "sort_type": "best_seller", "use_min_principle": true, "page_delay_seconds": 1, "max_pages_per_category": 3, "limit_strategy": "min", "auto_detect_max_pages": false, "page_detection_timeout": 30}, "_pagination_extended_description": "从工作流配置迁移的分页业务配置", "url_patterns": {"_description": "Walmart分页URL模式配置", "base_pattern": "{base_url}?{seo_params}&page={page_num}&affinityOverride=default", "seo_param_template": "seo={seo_value}", "seo_separator": "&"}, "validation": {"_description": "数据验证配置", "min_title_percentage": 90, "min_link_percentage": 90, "required_fields": ["title", "link", "page_number", "position_on_page"]}, "output": {"_description": "输出配置", "save_to_json": true, "output_directory": "output", "filename_template": "collected_product_urls_{timestamp}.json", "include_metadata": true, "include_pagination_summary": true}, "error_handling": {"_description": "错误处理配置", "max_retries": 3, "retry_delay_seconds": 2, "fallback_to_simple_scraping": true, "continue_on_page_error": true}, "performance": {"_description": "性能配置", "concurrent_requests": false, "request_timeout_seconds": 60, "page_load_timeout_seconds": 30, "memory_limit_mb": 512}}