{"_description": "Web Scraper APIs 配置文件", "_version": "1.0.0", "_last_updated": "2025-01-04", "default_api": "oxylabs", "_default_api_description": "默认使用的API服务商", "_supported_apis": ["oxylabs", "scrapingbee", "scraperapi"], "oxylabs": {"_description": "Oxylabs Web Scraper API 配置", "username": "johnzhong_2YBFQ", "password": "Zhqq_163comT", "api_url": "https://realtime.oxylabs.io/v1/queries", "timeout": 120, "max_retries": 3, "retry_delay": 5, "features": {"_description": "支持的功能特性", "javascript_rendering": true, "custom_parsing": true, "browser_instructions": true, "geo_location": true, "proxy_rotation": true, "session_management": true}, "default_params": {"_description": "默认请求参数", "source": "universal_ecommerce", "geo_location": "United States", "locale": "en-US", "render": "html"}, "rate_limits": {"_description": "速率限制配置", "requests_per_second": 2, "requests_per_minute": 100, "concurrent_requests": 5}}, "scrapingbee": {"_description": "ScrapingBee API 配置（未来扩展）", "api_key": "", "api_url": "https://app.scrapingbee.com/api/v1/", "timeout": 60, "max_retries": 3, "retry_delay": 3, "features": {"javascript_rendering": true, "custom_parsing": false, "browser_instructions": true, "geo_location": true, "proxy_rotation": true, "session_management": false}, "default_params": {"render_js": true, "premium_proxy": true, "country_code": "us"}}, "scraperapi": {"_description": "ScraperAPI 配置（未来扩展）", "api_key": "", "api_url": "http://api.scraperapi.com", "timeout": 60, "max_retries": 3, "retry_delay": 3, "features": {"javascript_rendering": true, "custom_parsing": false, "browser_instructions": false, "geo_location": true, "proxy_rotation": true, "session_management": false}, "default_params": {"render": true, "country_code": "us", "premium": true}}, "fallback_strategy": {"_description": "API失败时的回退策略", "enabled": true, "fallback_order": ["oxylabs", "scrapingbee", "scraperapi"], "max_fallback_attempts": 2}}