#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Walmart 抓取器包
基于新架构的模块化抓取系统
"""

# 新架构导入
from .workflow import (
    WorkflowManager, WorkflowResult, CategoryResult,
    WorkflowConfig, WorkflowConfigManager
)
from .modules import (
    BaseModule, ProductListModule,
    ProductDetailModule, PaginationModule
)

__version__ = "3.0.0"
__author__ = "Walmart Scraper Team"
__description__ = "基于新架构的模块化 Walmart 抓取系统"

# 主要导出
__all__ = [
    # 工作流管理
    "WorkflowManager",
    "WorkflowResult",
    "CategoryResult",
    "WorkflowConfig",
    "WorkflowConfigManager",

    # 功能模块
    "BaseModule",
    "ProductListModule",
    "ProductDetailModule",
    "PaginationModule"
]
