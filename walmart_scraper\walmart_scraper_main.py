#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Walmart 抓取器主程序
基于新架构的统一入口程序
"""

import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils import setup_logging, get_logger
from walmart_scraper.utils.output_manager import OutputManager
from walmart_scraper.workflow import WorkflowManager, WorkflowConfigManager


def get_user_input() -> Dict[str, Any]:
    """获取用户输入的配置参数"""
    print("🛒 Walmart 工作流抓取器")
    print("=" * 50)
    print("基于新架构的完整工作流抓取系统")
    print("📋 功能特性:")
    print("   ✅ 分类发现与筛选")
    print("   ✅ 智能分页处理") 
    print("   ✅ 产品列表抓取")
    print("   ✅ 产品详情抓取（可选）")
    print("   ✅ 进度恢复功能")
    print("   ✅ 按分类组织输出")
    print("   ✅ 完整的日志记录")
    print()
    
    # 获取用户配置
    config = {}
    
    print("⚙️  工作流配置:")
    print("-" * 30)
    
    # # 最大页数配置
    # try:
    #     max_pages = input("每个分类最大抓取页数 (默认5): ").strip()
    #     config['max_pages'] = int(max_pages) if max_pages else 5
    # except ValueError:
    #     config['max_pages'] = 5
    
    # # 产品详情配置
    # detail_choice = input("是否抓取产品详情? (y/N): ").strip().lower()
    # config['enable_details'] = detail_choice in ['y', 'yes']
    
    # # 进度恢复配置
    # resume_choice = input("是否尝试恢复之前的进度? (y/N): ").strip().lower()
    # config['resume'] = resume_choice in ['y', 'yes']
    config['resume'] = False
    # config['max_pages'] = 1  # 注释掉硬编码，使用配置文件中的值
    config['enable_details'] = False
    
    return config


# 移除硬编码配置函数，改为使用配置文件


def main():
    """主程序入口"""
    try:
        # 初始化日志系统
        setup_logging()
        logger = get_logger("walmart_scraper.main")
        logger.info("🚀 日志系统初始化完成")

        # 获取用户输入（简化版，只获取必要的覆盖参数）
        user_config = get_user_input()

        # 使用配置文件管理器加载配置
        from walmart_scraper.workflow.workflow_config import WorkflowConfigManager
        config_manager = WorkflowConfigManager()

        # 应用用户覆盖并加载配置
        workflow_config = config_manager.load_config_with_overrides(user_config)

        print("\n🚀 启动工作流")
        print("=" * 50)
        print(f"   目标分类: {[cat.name for cat in workflow_config.categories.categories if cat.enabled]}")
        print(f"   最大页数: {workflow_config.pagination.max_pages_per_category}")
        print(f"   产品详情: {'启用' if workflow_config.product_details.enabled else '禁用'}")
        print(f"   输出目录: {workflow_config.output.base_dir}")
        print()

        # 初始化输出管理器（使用工作流配置中的输出配置）
        output_manager = OutputManager(workflow_config.output)
        
        # 创建并运行工作流
        workflow_manager = WorkflowManager(config=workflow_config)
        # 设置输出管理器
        workflow_manager.output_manager = output_manager
        
        # 运行工作流
        result = workflow_manager.run_workflow(resume=user_config.get('resume', False))
        
        # 显示结果
        print("\n📊 工作流执行完成")
        print("=" * 50)
        if result.success:
            print("✅ 执行成功!")
            print(f"   总分类数: {result.total_categories}")
            print(f"   总产品数: {result.total_products}")
            print(f"   总页数: {result.total_pages}")
            print(f"   执行时间: {result.execution_time:.2f} 秒")
        else:
            print("❌ 执行失败!")
            print("   错误信息:")
            for error in result.errors:
                print(f"     • {error}")
        
        print(f"\n📁 输出目录: {workflow_config.output.base_dir}")
        print("\n😊 工作流执行完成")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
