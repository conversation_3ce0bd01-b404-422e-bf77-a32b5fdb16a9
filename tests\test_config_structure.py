#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置结构验证测试
验证分页配置迁移后的文件结构
"""

import os
import json
import logging

def test_pagination_config_structure():
    """测试pagination.json配置结构"""
    print("🧪 测试pagination.json配置结构...")
    
    try:
        # 构建配置文件路径
        config_path = os.path.join(
            os.path.dirname(__file__),
            '..', 'walmart_scraper', 'configs', 'modules', 'pagination.json'
        )
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 成功加载配置文件: {config_path}")
        
        # 检查必要的配置节
        required_sections = ['pagination', 'parsing_instructions', 'rendering_strategies']
        for section in required_sections:
            if section in config:
                print(f"  ✅ 包含配置节: {section}")
            else:
                print(f"  ❌ 缺少配置节: {section}")
                return False
        
        # 检查分页解析指令
        parsing_instructions = config.get('parsing_instructions', {})
        pagination_info = parsing_instructions.get('pagination_info', {})
        
        if pagination_info:
            print("  ✅ 包含分页解析指令")
            
            # 检查关键字段
            key_fields = ['current_page', 'total_pages', 'has_next_page']
            for field in key_fields:
                if field in pagination_info:
                    print(f"    ✅ 包含字段: {field}")
                else:
                    print(f"    ⚠️  缺少字段: {field}")
            
            # 检查新增字段
            new_fields = ['total_pages_fallback', 'total_pages_xpath_fallback', 'pagination_debug_info']
            for field in new_fields:
                if field in pagination_info:
                    print(f"    ✅ 包含新增字段: {field}")
        else:
            print("  ❌ 缺少分页解析指令")
            return False
        
        # 检查渲染策略
        rendering_strategies = config.get('rendering_strategies', {})
        pagination_strategy = rendering_strategies.get('with_pagination_wait', {})
        
        if pagination_strategy and 'browser_instructions' in pagination_strategy:
            instructions = pagination_strategy['browser_instructions']
            print(f"  ✅ 包含分页等待策略: {len(instructions)} 条指令")
            
            # 检查指令类型
            instruction_types = [instr.get('type') for instr in instructions]
            expected_types = ['wait_for_element', 'scroll', 'wait']
            
            for expected_type in expected_types:
                if expected_type in instruction_types:
                    print(f"    ✅ 包含指令类型: {expected_type}")
        else:
            print("  ❌ 缺少分页等待策略")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试pagination.json失败: {e}")
        return False

def test_product_list_config_cleanup():
    """测试product_list.json配置清理"""
    print("\n🧪 测试product_list.json配置清理...")
    
    try:
        # 构建配置文件路径
        config_path = os.path.join(
            os.path.dirname(__file__),
            '..', 'walmart_scraper', 'configs', 'modules', 'product_list.json'
        )
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 成功加载配置文件: {config_path}")
        
        # 检查是否已移除重复的分页配置
        parsing_instructions = config.get('parsing_instructions', {})
        
        if 'pagination_info' not in parsing_instructions:
            print("  ✅ 已移除重复的分页解析配置")
        else:
            print("  ⚠️  仍包含分页解析配置（可能需要进一步清理）")
        
        # 检查渲染策略
        rendering_strategies = config.get('rendering_strategies', {}).get('strategies', {})
        
        if 'with_pagination_wait' not in rendering_strategies:
            print("  ✅ 已移除重复的分页等待策略")
        else:
            print("  ⚠️  仍包含分页等待策略（可能需要进一步清理）")
        
        # 检查是否保留了产品相关配置
        if 'sorting' in config:
            print("  ✅ 保留了排序配置")
        
        if 'products' in parsing_instructions:
            print("  ✅ 保留了产品解析配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试product_list.json失败: {e}")
        return False

def test_css_selector_improvements():
    """测试CSS选择器改进"""
    print("\n🧪 测试CSS选择器改进...")
    
    try:
        # 构建配置文件路径
        config_path = os.path.join(
            os.path.dirname(__file__),
            '..', 'walmart_scraper', 'configs', 'modules', 'pagination.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        pagination_info = config.get('parsing_instructions', {}).get('pagination_info', {})
        
        # 检查当前页选择器
        current_page = pagination_info.get('current_page', {})
        current_page_selector = None
        for fn in current_page.get('_fns', []):
            if fn.get('_fn') == 'css_one':
                current_page_selector = fn.get('_args', [None])[0]
                break
        
        if current_page_selector and 'aria-current=\'page\'' in current_page_selector:
            print("  ✅ 当前页选择器使用aria-current属性")
        else:
            print("  ⚠️  当前页选择器可能需要优化")
        
        # 检查最大页数选择器
        total_pages = pagination_info.get('total_pages', {})
        total_pages_selector = None
        for fn in total_pages.get('_fns', []):
            if fn.get('_fn') == 'css':
                total_pages_selector = fn.get('_args', [None])[0]
                break
        
        if total_pages_selector:
            print(f"  ✅ 最大页数选择器: {total_pages_selector}")
        
        # 检查备用方案
        if 'total_pages_fallback' in pagination_info:
            print("  ✅ 包含最大页数备用方案")
        
        if 'total_pages_xpath_fallback' in pagination_info:
            print("  ✅ 包含XPath备用方案")
        
        # 检查调试信息
        if 'pagination_debug_info' in pagination_info:
            print("  ✅ 包含调试信息字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试CSS选择器改进失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始配置结构验证测试")
    print("=" * 60)
    
    tests = [
        test_pagination_config_structure,
        test_product_list_config_cleanup,
        test_css_selector_improvements
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            results.append(False)
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有配置结构测试通过！")
        print("\n📋 配置迁移总结:")
        print("  ✅ pagination.json: 统一管理分页相关配置")
        print("  ✅ product_list.json: 专注产品相关配置")
        print("  ✅ 分页解析器: 优化CSS选择器和备用方案")
        print("  ✅ 渲染策略: 增强分页等待机制")
        return True
    else:
        print(f"⚠️  {total - passed} 个测试失败，需要检查配置")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
