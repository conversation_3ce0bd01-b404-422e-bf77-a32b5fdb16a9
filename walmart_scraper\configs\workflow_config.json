{"_description": "Walmart 抓取器工作流配置文件 - 控制整个抓取工作流的行为", "_version": "2.0.0", "_last_updated": "2025-07-05", "_architecture_note": "模块具体配置已迁移到各自的模块配置文件中", "site": {"name": "walmart", "display_name": "Walmart", "description": "美国最大的零售商，产品种类丰富"}, "workflow": {"_description": "工作流编排配置", "enabled_modules": ["product_list", "pagination", "product_detail"], "_enabled_modules_description": "启用的模块列表，按执行顺序排列", "execution_mode": "sequential", "_execution_mode_description": "执行模式: sequential(顺序执行), parallel(并行执行)", "_execution_mode_options": ["sequential", "parallel"], "stop_on_error": false, "_stop_on_error_description": "遇到错误时是否停止整个工作流", "module_dependencies": {"_description": "模块依赖关系配置", "product_list": ["category"], "pagination": ["product_list"], "product_detail": ["product_list"]}, "_module_dependencies_description": "定义模块间的依赖关系"}, "_migration_notes": {"categories": "分类配置已迁移到 configs/modules/category.json", "pagination": "分页配置已迁移到 configs/modules/pagination.json", "sorting": "排序配置已迁移到 configs/modules/product_list.json", "product_details": "产品详情配置已迁移到 configs/modules/product_detail.json"}, "output": {"_description": "输出相关配置", "organization": "by_category", "_organization_description": "输出组织方式: by_category(按分类分别保存), unified(统一保存)", "_organization_options": ["by_category", "unified"], "base_dir": "output/{site_name}", "_base_dir_description": "输出基础目录路径，支持变量替换：{site_name}", "workflow_results_subdir": "workflow_results", "_workflow_results_subdir_description": "工作流总结文件的子目录名", "category_results_subdir": "", "_category_results_subdir_description": "分类结果文件的子目录名，空字符串表示直接在站点目录下", "save_intermediate": true, "_save_intermediate_description": "是否保存中间结果（每页产品、分类信息等）", "file_naming": {"_description": "文件命名规则配置", "category_info": "category_info.json", "products": "page_{page}_products.json", "details": "product_details/{product_id}.json", "summary": "category_summary.json", "workflow_summary": "workflow_summary.json"}, "_file_naming_description": "各类文件的命名模板，支持变量替换", "include_timestamp": true, "_include_timestamp_description": "是否在文件名中包含时间戳", "compress_output": false, "_compress_output_description": "是否压缩输出文件"}, "progress": {"_description": "进度管理配置", "enabled": true, "_enabled_description": "是否启用进度恢复功能", "checkpoint_frequency": 10, "_checkpoint_frequency_description": "检查点频率：每处理N个产品保存一次进度", "progress_file": "workflow_progress.json", "_progress_file_description": "进度文件名", "auto_cleanup": true, "_auto_cleanup_description": "工作流完成后是否自动清理进度文件", "backup_progress": true, "_backup_progress_description": "是否备份进度文件", "max_resume_attempts": 3, "_max_resume_attempts_description": "最大恢复尝试次数"}, "performance": {"_description": "性能相关配置", "request_delay": 1, "_request_delay_description": "请求之间的延迟时间（秒）", "timeout": 120, "_timeout_description": "单个请求的超时时间（秒）", "retry_attempts": 3, "_retry_attempts_description": "请求失败时的重试次数", "retry_delay": 5, "_retry_delay_description": "重试之间的延迟时间（秒）"}}