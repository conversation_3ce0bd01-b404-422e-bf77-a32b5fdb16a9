#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作流管理器
协调整个抓取流程：配置分类 → 产品列表 → 分页 → 产品详情
"""

import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .workflow_config import WorkflowConfig, WorkflowConfigManager
from .progress_manager import ProgressManager
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_workflow_logger, get_logger
from walmart_scraper.utils.output_manager import OutputManager


@dataclass
class WorkflowResult:
    """工作流执行结果"""
    success: bool
    total_categories: int
    total_products: int
    total_pages: int
    execution_time: float
    categories_results: Dict[str, Any]
    summary: Dict[str, Any]
    errors: List[str]


@dataclass
class CategoryResult:
    """单个分类的处理结果"""
    category_name: str
    category_url: str
    total_pages: int
    total_products: int
    products: List[Dict[str, Any]]
    product_details: List[Dict[str, Any]]
    execution_time: float
    success: bool
    errors: List[str]


class WorkflowManager:
    """工作流管理器 - 协调整个抓取流程"""
    
    def __init__(self, config: WorkflowConfig = None, config_path: str = None):
        """
        初始化工作流管理器
        
        Args:
            config: 工作流配置对象
            config_path: 配置文件路径
        """
        # 加载配置
        if config is None:
            config_manager = WorkflowConfigManager(config_path)
            self.config = config_manager.load_config()
        else:
            self.config = config
        
        # 初始化日志
        self.logger = get_workflow_logger("WORKFLOW_MANAGER")
        self.main_logger = get_logger("walmart_scraper.workflow")
        
        # 初始化组件（延迟加载，避免循环导入）
        self._product_list_module = None
        self._product_detail_module = None
        self._pagination_module = None
        
        # 初始化管理器
        self.progress_manager = ProgressManager(self.config)
        self.output_manager = OutputManager(self.config.output)
        
        # 工作流状态
        self.start_time = None
        self.current_step = "INIT"
        self.workflow_id = None
    
    def _initialize_modules(self):
        """延迟初始化功能模块组件"""
        if self._product_list_module is None:
            from ..modules.product_list_module import ProductListModule
            from ..modules.product_detail_module import ProductDetailModule
            from ..modules.pagination_module import PaginationModule

            self._product_list_module = ProductListModule(self.config)
            self._product_detail_module = ProductDetailModule(self.config)
            self._pagination_module = PaginationModule(self.config)
    
    def run_workflow(self, resume: bool = False) -> WorkflowResult:
        """
        运行完整的工作流
        
        Args:
            resume: 是否尝试恢复之前的进度
            
        Returns:
            工作流执行结果
        """
        self.start_time = time.time()
        self.workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.logger.info(f"🚀 开始执行工作流: {self.workflow_id}")
        self.main_logger.info(f"工作流配置: 目标分类={[cat.name for cat in self.config.categories.categories if cat.enabled]}, "
                             f"最大页数={self.config.pagination.max_pages_per_category}")
        
        try:
            # 检查是否可以恢复进度
            if resume and self.config.progress.enabled:
                if self.progress_manager.can_resume():
                    self.logger.info("📂 检测到可恢复的进度，尝试恢复...")
                    return self._resume_workflow()
            
            # 执行新的工作流
            return self._execute_new_workflow()
            
        except Exception as e:
            self.logger.error(f"❌ 工作流执行失败: {e}")
            return WorkflowResult(
                success=False,
                total_categories=0,
                total_products=0,
                total_pages=0,
                execution_time=time.time() - self.start_time,
                categories_results={},
                summary={"error": str(e)},
                errors=[str(e)]
            )
    
    def _execute_new_workflow(self) -> WorkflowResult:
        """执行新的工作流"""
        self._initialize_modules()

        # 步骤1: 获取配置的分类
        self._set_workflow_step("CATEGORY_PROCESSING")
        categories = self._get_configured_categories()
        if not categories:
            raise Exception("未找到启用的分类配置，无法继续执行工作流")

        # 步骤2: 处理每个分类
        categories_results = {}
        total_products = 0
        total_pages = 0
        all_errors = []
        
        for i, category in enumerate(categories, 1):
            category_name = category.get('name', f'Unknown_{i}')
            self.logger.info(f"📂 处理分类 {i}/{len(categories)}: {category_name}")

            try:
                category_result = self._process_category(category)
                categories_results[category_name] = category_result
                total_products += category_result.total_products
                total_pages += category_result.total_pages
                
                if category_result.errors:
                    all_errors.extend(category_result.errors)
                
                # 保存进度
                if self.config.progress.enabled:
                    self.progress_manager.save_category_progress(
                        category['name'], category_result
                    )
                
            except Exception as e:
                error_msg = f"分类 {category['name']} 处理失败: {e}"
                self.logger.error(f"❌ {error_msg}")
                all_errors.append(error_msg)
                
                # 创建失败的分类结果
                categories_results[category['name']] = CategoryResult(
                    category_name=category['name'],
                    category_url=category.get('full_url', ''),
                    total_pages=0,
                    total_products=0,
                    products=[],
                    product_details=[],
                    execution_time=0,
                    success=False,
                    errors=[error_msg]
                )
        
        # 步骤3: 生成最终结果
        self._set_workflow_step("OUTPUT")
        execution_time = time.time() - self.start_time
        
        workflow_result = WorkflowResult(
            success=len(all_errors) == 0,
            total_categories=len(categories),
            total_products=total_products,
            total_pages=total_pages,
            execution_time=execution_time,
            categories_results=categories_results,
            summary=self._generate_summary(categories_results, execution_time),
            errors=all_errors
        )
        
        # 保存工作流结果
        self.output_manager.save_workflow_result(workflow_result)
        
        # 清理进度文件
        if self.config.progress.enabled and self.config.progress.auto_cleanup:
            self.progress_manager.cleanup_progress()
        
        self.logger.info(f"✅ 工作流执行完成: 处理了{len(categories)}个分类, "
                        f"获取了{total_products}个产品, 耗时{execution_time:.2f}秒")
        
        return workflow_result
    
    def _get_configured_categories(self) -> List[Dict[str, Any]]:
        """获取配置的分类"""
        self.logger.info("📂 获取配置的分类...")

        # 从配置中获取启用的分类
        enabled_categories = []
        for category_item in self.config.categories.categories:
            if category_item.enabled:
                category_dict = {
                    'name': category_item.name,
                    'url': category_item.url,
                    'description': category_item.description
                }
                enabled_categories.append(category_dict)

        self.logger.info(f"📋 找到 {len(enabled_categories)} 个启用的分类")
        for cat in enabled_categories:
            self.logger.debug(f"   ✓ {cat['name']}")

        return enabled_categories

    
    def _process_category(self, category: Dict[str, Any]) -> CategoryResult:
        """处理单个分类"""
        category_name = category['name']
        category_url = category.get('url', '')
        
        self.logger.info(f"📂 开始处理分类: {category_name}")
        start_time = time.time()
        
        try:
            # 获取排序配置（如果启用）
            sorting_config = {}
            sorted_category_url = category_url
            if self.config.sorting.enabled:
                if self.config.sorting.method == "url_param":
                    sorted_category_url = self._apply_url_sorting(category_url)
                    self.logger.info(f"🔄 将应用URL排序: {self.config.sorting.default_sort}")
                    self.logger.info(f"🌐 排序后URL: {sorted_category_url}")
                else:
                    sorting_config = self._get_sorting_browser_instructions()
                    if sorting_config:
                        self.logger.info(f"🔄 将应用浏览器交互排序: {self.config.sorting.default_sort}")
                    else:
                        self.logger.warning(f"⚠️  排序配置失败，将使用默认排序")

            # 获取分页信息（使用排序配置）
            max_pages = self._get_effective_max_pages(sorted_category_url, sorting_config)
            self.logger.info(f"📄 分类 {category_name} 将抓取 {max_pages} 页")

            # 抓取所有页面的产品
            all_products = []
            actual_pages = 0

            for page_num in range(1, max_pages + 1):
                self.logger.info(f"📄 抓取第 {page_num} 页...")

                page_url = self._pagination_module.build_page_url(sorted_category_url, page_num)
                page_result = self._product_list_module.scrape_page_with_sorting(page_url, sorting_config)
                
                if not page_result or not page_result.get('products'):
                    self.logger.warning(f"⚠️  第 {page_num} 页无产品数据，停止抓取")
                    break
                
                products = page_result['products']
                # 应用产品数量限制
                max_products = self.config.pagination.max_products_per_page
                if len(products) > max_products:
                    products = products[:max_products]
                    self.logger.info(f"📦 第 {page_num} 页产品数量限制为 {max_products} 个")
                
                all_products.extend(products)
                actual_pages += 1
                
                # 保存页面结果
                if self.config.output.save_intermediate:
                    self.output_manager.save_page_result(
                        category_name, page_num, products
                    )
                
                # 请求间隔
                if page_num < max_pages:
                    time.sleep(self.config.performance.request_delay)
            
            # 抓取产品详情（如果启用）
            product_details = []
            if self.config.product_details.enabled and all_products:
                product_details = self._scrape_product_details(
                    category_name, all_products
                )
            
            execution_time = time.time() - start_time
            
            # 创建分类结果
            category_result = CategoryResult(
                category_name=category_name,
                category_url=category_url,
                total_pages=actual_pages,
                total_products=len(all_products),
                products=all_products,
                product_details=product_details,
                execution_time=execution_time,
                success=True,
                errors=[]
            )
            
            # 保存分类结果
            self.output_manager.save_category_result(category_result)
            
            self.logger.info(f"✅ 分类 {category_name} 处理完成: "
                           f"{len(all_products)} 个产品, {actual_pages} 页, "
                           f"耗时 {execution_time:.2f} 秒")
            
            return category_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"分类 {category_name} 处理失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            
            return CategoryResult(
                category_name=category_name,
                category_url=category_url,
                total_pages=0,
                total_products=0,
                products=[],
                product_details=[],
                execution_time=execution_time,
                success=False,
                errors=[error_msg]
            )
    
    def _get_sorting_browser_instructions(self, sort_option: str = None) -> Dict[str, Any]:
        """
        获取排序的浏览器交互指令

        Args:
            sort_option: 排序选项，如果为None则使用默认排序

        Returns:
            包含browser_instructions的字典，如果排序失败则返回空字典
        """
        if not self.config.sorting.enabled:
            return {}

        # 使用指定的排序选项或默认排序
        sort_key = sort_option or self.config.sorting.default_sort
        sort_config = self.config.sorting.options.get(sort_key)

        if not sort_config:
            self.logger.error(f"❌ 排序配置错误: 未找到排序选项 '{sort_key}'")
            return {}

        # 检查排序方法
        if self.config.sorting.method != 'browser_interaction':
            self.logger.warning(f"⚠️  排序方法不是browser_interaction: {self.config.sorting.method}")
            return {}

        try:
            # 构建浏览器交互指令
            interaction_config = self.config.sorting.interaction_config

            browser_instructions = [
                {
                    "type": "click",
                    "selector": {
                        "type": "css",
                        "value": interaction_config.sort_button_selector
                    }
                },
                {
                    "type": "wait_for_element",
                    "selector": {
                        "type": "css",
                        "value": sort_config["selector"]
                    },
                    "timeout_s": interaction_config.wait_timeout // 1000
                },
                {
                    "type": "click",
                    "selector": {
                        "type": "css",
                        "value": sort_config["selector"]
                    }
                },
                {
                    "type": "wait",
                    "wait_time_s": interaction_config.network_idle_timeout // 1000
                }
            ]

            self.logger.debug(f"🔄 生成排序交互指令: {sort_key} -> {sort_config['display_name']}")

            return {
                "browser_instructions": browser_instructions,
                "render": "html"
            }

        except Exception as e:
            self.logger.error(f"❌ 生成排序交互指令失败: {e}")
            return {}

    def _apply_url_sorting(self, base_url: str, sort_option: str = None) -> str:
        """
        应用URL参数排序

        Args:
            base_url: 基础URL
            sort_option: 排序选项，如果为None则使用默认排序

        Returns:
            应用排序后的URL
        """
        if not self.config.sorting.enabled:
            return base_url

        # 使用指定的排序选项或默认排序
        sort_key = sort_option or self.config.sorting.default_sort
        sort_config = self.config.sorting.options.get(sort_key)

        if not sort_config:
            self.logger.error(f"❌ 排序配置错误: 未找到排序选项 '{sort_key}'")
            return base_url

        # 检查排序方法
        if self.config.sorting.method != 'url_param':
            self.logger.warning(f"⚠️  排序方法不是url_param: {self.config.sorting.method}")
            return base_url

        try:
            import urllib.parse

            # 解析URL
            parsed = urllib.parse.urlparse(base_url)
            query_params = urllib.parse.parse_qs(parsed.query)

            # 添加排序参数
            url_param = sort_config.get('url_param', 'sort')
            url_value = sort_config.get('url_value', sort_key)
            query_params[url_param] = [url_value]

            # 添加affinityOverride参数（Walmart特有）
            if 'affinityOverride' not in query_params:
                query_params['affinityOverride'] = ['default']

            # 重新构建URL
            new_query = urllib.parse.urlencode(query_params, doseq=True)
            sorted_url = urllib.parse.urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            self.logger.debug(f"🔄 URL排序: {sort_key} -> {sort_config.get('display_name')}")
            self.logger.debug(f"   原始URL: {base_url}")
            self.logger.debug(f"   排序URL: {sorted_url}")

            return sorted_url

        except Exception as e:
            self.logger.error(f"❌ URL排序失败: {e}")
            return base_url
    
    def _get_effective_max_pages(self, category_url: str, sorting_config: Dict[str, Any] = None) -> int:
        """
        获取有效的最大页数

        Args:
            category_url: 分类URL
            sorting_config: 排序配置，用于页数检测

        Returns:
            有效的最大页数
        """
        config_max = self.config.pagination.max_pages_per_category

        if not self.config.pagination.auto_detect_max_pages:
            return config_max

        # 检测实际最大页数
        try:
            actual_max = self._pagination_module.detect_max_pages_with_sorting(category_url, sorting_config)

            if self.config.pagination.limit_strategy == "min":
                return min(actual_max, config_max)
            elif self.config.pagination.limit_strategy == "config_only":
                return config_max
            elif self.config.pagination.limit_strategy == "actual_only":
                return actual_max

        except Exception as e:
            self.logger.warning(f"⚠️  页数检测失败，使用配置值: {e}")
            return config_max

        return config_max
    
    def _scrape_product_details(self, category_name: str, 
                               products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """抓取产品详情"""
        if not self.config.product_details.enabled:
            return []
        
        self.logger.info(f"🔍 开始抓取 {category_name} 的产品详情...")
        
        # 根据模式选择产品
        if self.config.product_details.mode == "all":
            target_products = products
        elif self.config.product_details.mode == "sample":
            sample_size = min(self.config.product_details.sample_size, len(products))
            if self.config.product_details.sample_strategy == "random":
                import random
                target_products = random.sample(products, sample_size)
            elif self.config.product_details.sample_strategy == "first":
                target_products = products[:sample_size]
            elif self.config.product_details.sample_strategy == "last":
                target_products = products[-sample_size:]
            else:
                target_products = products[:sample_size]
        else:
            return []
        
        self.logger.info(f"📦 将抓取 {len(target_products)} 个产品的详情")
        
        # 批量抓取产品详情
        return self._product_detail_module.batch_scrape_details(
            category_name, target_products
        )
    
    def _generate_summary(self, categories_results: Dict[str, CategoryResult], 
                         execution_time: float) -> Dict[str, Any]:
        """生成工作流摘要"""
        successful_categories = [
            result for result in categories_results.values() if result.success
        ]
        
        return {
            "workflow_id": self.workflow_id,
            "execution_time": execution_time,
            "total_categories": len(categories_results),
            "successful_categories": len(successful_categories),
            "failed_categories": len(categories_results) - len(successful_categories),
            "total_products": sum(result.total_products for result in categories_results.values()),
            "total_pages": sum(result.total_pages for result in categories_results.values()),
            "average_products_per_category": sum(result.total_products for result in successful_categories) / len(successful_categories) if successful_categories else 0,
            "config_summary": {
                "target_categories": [cat.name for cat in self.config.categories.categories if cat.enabled],
                "max_pages_per_category": self.config.pagination.max_pages_per_category,
                "product_details_enabled": self.config.product_details.enabled,
                "sorting_enabled": self.config.sorting.enabled
            }
        }
    
    def _set_workflow_step(self, step: str):
        """设置当前工作流步骤"""
        self.current_step = step
        self.logger = get_workflow_logger(step)
    
    def _resume_workflow(self) -> WorkflowResult:
        """恢复工作流执行"""
        # TODO: 实现进度恢复逻辑
        self.logger.info("📂 进度恢复功能开发中...")
        return self._execute_new_workflow()


# 便捷函数
def run_walmart_workflow(config_path: str = None, resume: bool = False) -> WorkflowResult:
    """运行 Walmart 工作流的便捷函数"""
    manager = WorkflowManager(config_path=config_path)
    return manager.run_workflow(resume=resume)
