#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
解析参数优化测试
验证只有在有解析指令时才添加 "parse": True 参数
"""

import os
import sys
import json
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from scraper_apis.oxylabs_api import OxylabsAPI


class ParseParameterOptimizationTest:
    """解析参数优化测试类"""
    
    def __init__(self):
        """初始化测试"""
        # 创建模拟的API配置
        self.api_config = {
            "username": "test_user",
            "password": "test_pass"
        }
        
        # 创建API实例
        self.api = OxylabsAPI(self.api_config)
        
        # 测试URL
        self.test_url = "https://www.walmart.com/test"
    
    def test_scrape_with_parsing_empty_instructions(self):
        """测试空解析指令时不添加parse参数"""
        print("🧪 测试1：空解析指令时不添加parse参数")
        
        # 模拟_make_request方法来捕获payload
        captured_payload = {}
        
        def mock_make_request(payload):
            captured_payload.update(payload)
            return {"success": True}
        
        with patch.object(self.api, '_make_request', side_effect=mock_make_request):
            # 测试空字典
            self.api.scrape_with_parsing(self.test_url, {})
            
            if "parse" not in captured_payload:
                print("  ✅ 空字典：未添加parse参数")
            else:
                print("  ❌ 空字典：错误地添加了parse参数")
                return False
            
            # 测试None
            captured_payload.clear()
            self.api.scrape_with_parsing(self.test_url, None)
            
            if "parse" not in captured_payload:
                print("  ✅ None值：未添加parse参数")
            else:
                print("  ❌ None值：错误地添加了parse参数")
                return False
        
        return True
    
    def test_scrape_with_parsing_valid_instructions(self):
        """测试有效解析指令时添加parse参数"""
        print("\n🧪 测试2：有效解析指令时添加parse参数")
        
        # 模拟_make_request方法来捕获payload
        captured_payload = {}
        
        def mock_make_request(payload):
            captured_payload.update(payload)
            return {"success": True}
        
        # 测试有效的解析指令
        valid_instructions = {
            "products": {
                "_fns": [
                    {"_fn": "css", "_args": [".product"]}
                ]
            }
        }
        
        with patch.object(self.api, '_make_request', side_effect=mock_make_request):
            self.api.scrape_with_parsing(self.test_url, valid_instructions)
            
            if captured_payload.get("parse") is True:
                print("  ✅ 有效指令：正确添加了parse参数")
            else:
                print("  ❌ 有效指令：未添加parse参数")
                return False
            
            if captured_payload.get("parsing_instructions") == valid_instructions:
                print("  ✅ 有效指令：正确传递了解析指令")
            else:
                print("  ❌ 有效指令：解析指令传递错误")
                return False
        
        return True
    
    def test_scrape_with_browser_instructions_consistency(self):
        """测试browser_instructions方法的一致性"""
        print("\n🧪 测试3：browser_instructions方法的一致性")
        
        # 模拟_make_request方法来捕获payload
        captured_payloads = []
        
        def mock_make_request(payload):
            captured_payloads.append(payload.copy())
            return {"success": True}
        
        browser_instructions = [
            {"type": "wait", "wait_time_s": 5}
        ]
        
        with patch.object(self.api, '_make_request', side_effect=mock_make_request):
            # 测试无解析指令
            self.api.scrape_with_browser_instructions(
                self.test_url, 
                browser_instructions, 
                parsing_instructions=None
            )
            
            # 测试空解析指令
            self.api.scrape_with_browser_instructions(
                self.test_url, 
                browser_instructions, 
                parsing_instructions={}
            )
            
            # 测试有效解析指令
            valid_instructions = {"products": {"_fns": [{"_fn": "css", "_args": [".product"]}]}}
            self.api.scrape_with_browser_instructions(
                self.test_url, 
                browser_instructions, 
                parsing_instructions=valid_instructions
            )
        
        # 检查结果
        if len(captured_payloads) == 3:
            # 第一个请求（None）
            if "parse" not in captured_payloads[0]:
                print("  ✅ None解析指令：未添加parse参数")
            else:
                print("  ❌ None解析指令：错误地添加了parse参数")
                return False
            
            # 第二个请求（空字典）
            if "parse" not in captured_payloads[1]:
                print("  ✅ 空解析指令：未添加parse参数")
            else:
                print("  ❌ 空解析指令：错误地添加了parse参数")
                return False
            
            # 第三个请求（有效指令）
            if captured_payloads[2].get("parse") is True:
                print("  ✅ 有效解析指令：正确添加了parse参数")
            else:
                print("  ❌ 有效解析指令：未添加parse参数")
                return False
        else:
            print(f"  ❌ 请求数量错误：期望3个，实际{len(captured_payloads)}个")
            return False
        
        return True
    
    def test_payload_structure_optimization(self):
        """测试载荷结构优化"""
        print("\n🧪 测试4：载荷结构优化")
        
        # 模拟_make_request方法来捕获payload
        captured_payload = {}
        
        def mock_make_request(payload):
            captured_payload.update(payload)
            return {"success": True}
        
        with patch.object(self.api, '_make_request', side_effect=mock_make_request):
            # 测试基础抓取（无解析）
            self.api.scrape_with_parsing(self.test_url, None)
            
            # 检查基础字段
            required_fields = ["source", "url", "render"]
            for field in required_fields:
                if field in captured_payload:
                    print(f"  ✅ 包含必要字段: {field}")
                else:
                    print(f"  ❌ 缺少必要字段: {field}")
                    return False
            
            # 检查不应该存在的字段
            unwanted_fields = ["parse", "parsing_instructions"]
            for field in unwanted_fields:
                if field not in captured_payload:
                    print(f"  ✅ 正确排除字段: {field}")
                else:
                    print(f"  ❌ 错误包含字段: {field}")
                    return False
        
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始解析参数优化测试")
        print("=" * 60)
        
        tests = [
            self.test_scrape_with_parsing_empty_instructions,
            self.test_scrape_with_parsing_valid_instructions,
            self.test_scrape_with_browser_instructions_consistency,
            self.test_payload_structure_optimization
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                print("-" * 40)
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
                results.append(False)
        
        # 总结
        passed = sum(results)
        total = len(results)
        
        print("=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有解析参数优化测试通过！")
            print("\n📋 优化总结:")
            print("  ✅ 空解析指令时不添加parse参数")
            print("  ✅ 有效解析指令时正确添加parse参数")
            print("  ✅ browser_instructions方法保持一致性")
            print("  ✅ 载荷结构得到优化")
            print("\n💡 优化效果:")
            print("  🔧 减少不必要的API参数")
            print("  ⚡ 提高API请求效率")
            print("  🛡️ 避免潜在的解析错误")
            return True
        else:
            print(f"⚠️  {total - passed} 个测试失败，需要检查实现")
            return False


def main():
    """主函数"""
    try:
        # 运行测试
        test = ParseParameterOptimizationTest()
        success = test.run_all_tests()
        
        if success:
            print("\n✅ 解析参数优化测试全部通过！")
            return 0
        else:
            print("\n❌ 解析参数优化测试存在问题，请检查实现")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
