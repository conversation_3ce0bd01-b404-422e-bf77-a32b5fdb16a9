#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分页配置迁移测试
测试新的配置架构：分页解析器配置从product_list.json迁移到pagination.json
"""

import sys
import os
import json
import logging
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from walmart_scraper.modules.product_list_module import ProductListModule
    from walmart_scraper.modules.pagination_module import PaginationModule
    from walmart_scraper.workflow.workflow_config import WorkflowConfig
    from walmart_scraper.utils.logger_manager import get_logger
except ImportError:
    # 简化版本，直接使用logging
    def get_logger(name):
        return logging.getLogger(name)


class PaginationConfigMigrationTest:
    """分页配置迁移测试类"""
    
    def __init__(self):
        """初始化测试"""
        self.logger = get_logger(self.__class__.__name__)
        
        # 测试URL
        self.test_url = "https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_9169445?sort=best_seller&affinityOverride=default"
        
        # 初始化模块
        try:
            # 创建简单配置
            self.config = self._create_test_config()
            self.product_module = ProductListModule(self.config)
            self.pagination_module = PaginationModule(self.config)
            
            self.logger.info("✅ 测试模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 测试模块初始化失败: {e}")
            raise
    
    def _create_test_config(self) -> WorkflowConfig:
        """创建测试配置"""
        # 简单的测试配置
        test_config_dict = {
            'pagination': {
                'max_pages_per_category': 2,
                'max_products_per_page': 20,
                'limit_strategy': 'min',
                'auto_detect_max_pages': True
            },
            'sorting': {
                'enabled': True,
                'method': 'url_param',
                'default_sort': 'best_seller'
            }
        }
        
        return WorkflowConfig(test_config_dict)
    
    def test_pagination_parsing_instructions(self):
        """测试分页解析指令获取"""
        self.logger.info("🧪 测试1：分页解析指令获取")
        
        try:
            # 获取产品解析指令（应该包含从pagination.json加载的分页解析）
            parsing_instructions = self.product_module.get_product_parsing_instructions()
            
            # 检查是否包含分页信息
            if 'pagination_info' in parsing_instructions:
                pagination_info = parsing_instructions['pagination_info']
                self.logger.info("✅ 成功获取分页解析指令")
                
                # 检查关键字段
                required_fields = ['current_page', 'total_pages', 'has_next_page']
                for field in required_fields:
                    if field in pagination_info:
                        self.logger.info(f"  ✅ 包含字段: {field}")
                    else:
                        self.logger.warning(f"  ⚠️  缺少字段: {field}")
                
                # 检查新增的调试字段
                if 'pagination_debug_info' in pagination_info:
                    self.logger.info("  ✅ 包含调试信息字段")
                
                # 检查备用方案
                fallback_fields = ['total_pages_fallback', 'total_pages_xpath_fallback']
                for field in fallback_fields:
                    if field in pagination_info:
                        self.logger.info(f"  ✅ 包含备用方案: {field}")
                
                return True
            else:
                self.logger.error("❌ 未找到分页解析指令")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 测试分页解析指令失败: {e}")
            return False
    
    def test_pagination_rendering_strategy(self):
        """测试分页渲染策略获取"""
        self.logger.info("🧪 测试2：分页渲染策略获取")
        
        try:
            # 获取分页渲染策略
            strategy = self.pagination_module._get_pagination_rendering_strategy()
            
            if strategy and 'browser_instructions' in strategy:
                instructions = strategy['browser_instructions']
                self.logger.info(f"✅ 成功获取分页渲染策略: {len(instructions)} 条指令")
                
                # 检查关键指令
                instruction_types = [instr.get('type') for instr in instructions]
                expected_types = ['wait_for_element', 'scroll', 'wait']
                
                for expected_type in expected_types:
                    if expected_type in instruction_types:
                        self.logger.info(f"  ✅ 包含指令类型: {expected_type}")
                    else:
                        self.logger.warning(f"  ⚠️  缺少指令类型: {expected_type}")
                
                return True
            else:
                self.logger.error("❌ 未找到分页渲染策略")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 测试分页渲染策略失败: {e}")
            return False
    
    def test_config_file_structure(self):
        """测试配置文件结构"""
        self.logger.info("🧪 测试3：配置文件结构验证")
        
        try:
            # 检查pagination.json结构
            pagination_config_path = os.path.join(
                os.path.dirname(__file__),
                '..', 'walmart_scraper', 'configs', 'modules', 'pagination.json'
            )
            
            with open(pagination_config_path, 'r', encoding='utf-8') as f:
                pagination_config = json.load(f)
            
            # 检查必要的配置节
            required_sections = ['pagination', 'parsing_instructions', 'rendering_strategies']
            for section in required_sections:
                if section in pagination_config:
                    self.logger.info(f"  ✅ pagination.json包含: {section}")
                else:
                    self.logger.error(f"  ❌ pagination.json缺少: {section}")
                    return False
            
            # 检查parsing_instructions.pagination_info
            if 'pagination_info' in pagination_config.get('parsing_instructions', {}):
                self.logger.info("  ✅ pagination.json包含分页解析指令")
            else:
                self.logger.error("  ❌ pagination.json缺少分页解析指令")
                return False
            
            # 检查rendering_strategies.with_pagination_wait
            if 'with_pagination_wait' in pagination_config.get('rendering_strategies', {}):
                self.logger.info("  ✅ pagination.json包含分页等待策略")
            else:
                self.logger.error("  ❌ pagination.json缺少分页等待策略")
                return False
            
            # 检查product_list.json是否已清理
            product_config_path = os.path.join(
                os.path.dirname(__file__),
                '..', 'walmart_scraper', 'configs', 'modules', 'product_list.json'
            )
            
            with open(product_config_path, 'r', encoding='utf-8') as f:
                product_config = json.load(f)
            
            # 检查是否已移除重复配置
            parsing_instructions = product_config.get('parsing_instructions', {})
            if 'pagination_info' not in parsing_instructions:
                self.logger.info("  ✅ product_list.json已移除重复的分页解析配置")
            else:
                self.logger.warning("  ⚠️  product_list.json仍包含分页解析配置")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 测试配置文件结构失败: {e}")
            return False
    
    def test_integration(self):
        """集成测试：实际抓取验证"""
        self.logger.info("🧪 测试4：集成测试（实际抓取验证）")
        
        try:
            # 使用新配置进行实际抓取测试
            result = self.product_module.scrape_page(self.test_url)
            
            if result and result.get('success'):
                self.logger.info("✅ 页面抓取成功")
                
                # 检查分页信息
                pagination_info = result.get('pagination_info', {})
                if pagination_info:
                    self.logger.info("✅ 成功获取分页信息")
                    self.logger.info(f"  当前页: {pagination_info.get('current_page')}")
                    self.logger.info(f"  总页数: {pagination_info.get('total_pages')}")
                    self.logger.info(f"  有下一页: {pagination_info.get('has_next_page')}")
                    
                    # 检查调试信息
                    debug_info = pagination_info.get('pagination_debug_info', '')
                    if debug_info:
                        self.logger.info(f"✅ 获取到调试信息: {len(debug_info)} 字符")
                    
                    return True
                else:
                    self.logger.error("❌ 未获取到分页信息")
                    return False
            else:
                self.logger.error("❌ 页面抓取失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 集成测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.logger.info("🚀 开始分页配置迁移测试")
        self.logger.info("=" * 60)
        
        tests = [
            self.test_config_file_structure,
            self.test_pagination_parsing_instructions,
            self.test_pagination_rendering_strategy,
            self.test_integration
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                self.logger.info("-" * 40)
            except Exception as e:
                self.logger.error(f"❌ 测试执行异常: {e}")
                results.append(False)
        
        # 总结
        passed = sum(results)
        total = len(results)
        
        self.logger.info("=" * 60)
        self.logger.info(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            self.logger.info("🎉 所有测试通过！配置迁移成功！")
        else:
            self.logger.warning(f"⚠️  {total - passed} 个测试失败，需要检查配置")
        
        return passed == total


def main():
    """主函数"""
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 运行测试
        test = PaginationConfigMigrationTest()
        success = test.run_all_tests()
        
        if success:
            print("\n✅ 分页配置迁移测试全部通过！")
            return 0
        else:
            print("\n❌ 分页配置迁移测试存在问题，请检查日志")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
