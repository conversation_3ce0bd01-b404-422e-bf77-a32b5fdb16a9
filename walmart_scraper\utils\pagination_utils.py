#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页工具模块 - 处理Walmart分页URL构建和相关逻辑
"""

import urllib.parse
from typing import Optional, Dict, Any
from utils.logger.logger import get_logger

class PaginationUtils:
    """分页工具类"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def build_walmart_pagination_url(self, base_url: str, page_num: int = 1) -> str:
        """
        构建Walmart分页URL
        
        基于验证结果的URL模式：
        URL1: https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_9169445
        URL2: https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_9169445?seo=home&seo=christmas-trees&seo=4044_2634414_7472650_9169445&page=2&affinityOverride=default
        
        Args:
            base_url: 基础分类URL
            page_num: 页码（从1开始）
            
        Returns:
            构建的分页URL
            
        Raises:
            ValueError: 当page_num小于1或base_url格式不正确时
        """
        if page_num < 1:
            raise ValueError(f"页码必须大于等于1，当前值: {page_num}")
        
        if not base_url or not isinstance(base_url, str):
            raise ValueError(f"base_url必须是非空字符串，当前值: {base_url}")
        
        # 第1页直接返回原URL
        if page_num == 1:
            self.logger.debug(f"第1页，返回原URL: {base_url}")
            return base_url
        
        try:
            # 解析URL获取路径信息
            parsed = urllib.parse.urlparse(base_url)
            if not parsed.path:
                raise ValueError(f"URL路径为空: {base_url}")

            # 获取现有的查询参数
            existing_params = urllib.parse.parse_qs(parsed.query)

            # 提取路径部分：/browse/home/<USER>/4044_2634414_7472650_9169445
            # 分割为：['', 'browse', 'home', 'christmas-trees', '4044_2634414_7472650_9169445']
            path_parts = parsed.path.strip('/').split('/')

            if len(path_parts) < 2 or path_parts[0] != 'browse':
                raise ValueError(f"URL格式不正确，期望以/browse/开头: {base_url}")

            # 构建SEO参数：跳过'browse'，使用后续部分
            seo_parts = path_parts[1:]  # ['home', 'christmas-trees', '4044_2634414_7472650_9169445']

            if not seo_parts:
                raise ValueError(f"无法提取SEO参数: {base_url}")

            # 构建查询参数字典
            query_params = {}

            # 保留现有参数（如sort等）
            for key, values in existing_params.items():
                query_params[key] = values

            # 添加SEO参数
            query_params['seo'] = [urllib.parse.quote(part) for part in seo_parts if part]

            # 添加分页参数
            query_params['page'] = [str(page_num)]

            # 添加亲和性参数（如果不存在）
            if 'affinityOverride' not in query_params:
                query_params['affinityOverride'] = ['default']

            # 重新构建URL
            new_query = urllib.parse.urlencode(query_params, doseq=True)
            final_url = urllib.parse.urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            self.logger.debug(f"构建分页URL: 第{page_num}页 -> {final_url}")
            return final_url
            
        except Exception as e:
            self.logger.error(f"构建分页URL失败: {e}")
            raise ValueError(f"构建分页URL失败: {e}")
    
    def extract_page_number_from_url(self, url: str) -> int:
        """
        从URL中提取页码
        
        Args:
            url: 包含分页参数的URL
            
        Returns:
            页码，如果没有找到则返回1
        """
        try:
            parsed = urllib.parse.urlparse(url)
            query_params = urllib.parse.parse_qs(parsed.query)
            
            if 'page' in query_params and query_params['page']:
                page_num = int(query_params['page'][0])
                self.logger.debug(f"从URL提取页码: {page_num}")
                return page_num
            else:
                self.logger.debug("URL中未找到page参数，返回第1页")
                return 1
                
        except (ValueError, IndexError) as e:
            self.logger.warning(f"提取页码失败: {e}，返回第1页")
            return 1
    
    def validate_pagination_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和标准化分页配置
        
        Args:
            config: 分页配置字典
            
        Returns:
            验证后的配置字典
            
        Raises:
            ValueError: 当配置不合法时
        """
        if not isinstance(config, dict):
            raise ValueError("分页配置必须是字典类型")
        
        # 默认配置
        default_config = {
            'max_pages': 5,
            'max_products_per_page': 20,
            'use_min_principle': True
        }
        
        # 合并配置
        validated_config = {**default_config, **config}
        
        # 验证数值范围
        if validated_config['max_pages'] < 1:
            raise ValueError(f"max_pages必须大于等于1，当前值: {validated_config['max_pages']}")
        
        if validated_config['max_pages'] > 100:
            self.logger.warning(f"max_pages过大({validated_config['max_pages']})，建议不超过100页")
        
        if validated_config['max_products_per_page'] < 1:
            raise ValueError(f"max_products_per_page必须大于等于1，当前值: {validated_config['max_products_per_page']}")
        
        # 移除硬编码的50个产品限制，让用户自由配置
        # 实际抓取数量以Walmart网站实际返回的产品数量为准
        

        
        self.logger.debug(f"分页配置验证完成: {validated_config}")
        return validated_config
    
    def apply_min_principle(self, config: Dict[str, Any], actual_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用最小值原则
        
        配置的 vs 实际的，使用较小值
        
        Args:
            config: 用户配置
            actual_data: 实际抓取到的数据信息
            
        Returns:
            应用最小值原则后的配置
        """
        if not config.get('use_min_principle', True):
            self.logger.debug("未启用最小值原则，使用原配置")
            return config
        
        adjusted_config = config.copy()
        
        # 应用页数限制
        actual_total_pages = actual_data.get('total_pages')
        if actual_total_pages and actual_total_pages > 0:
            original_max_pages = adjusted_config['max_pages']
            adjusted_config['max_pages'] = min(original_max_pages, actual_total_pages)
            
            if adjusted_config['max_pages'] != original_max_pages:
                self.logger.info(f"应用最小值原则 - 页数: {original_max_pages} -> {adjusted_config['max_pages']}")
        
        # 应用每页产品数限制
        actual_products_per_page = actual_data.get('products_per_page')
        if actual_products_per_page and actual_products_per_page > 0:
            original_max_products = adjusted_config['max_products_per_page']
            adjusted_config['max_products_per_page'] = min(original_max_products, actual_products_per_page)

            # 详细显示产品数量信息
            self.logger.info(f"📊 页面产品数量分析: 配置={original_max_products}, 实际={actual_products_per_page}, 最终={adjusted_config['max_products_per_page']}")

            if adjusted_config['max_products_per_page'] != original_max_products:
                self.logger.info(f"应用最小值原则 - 每页产品数: {original_max_products} -> {adjusted_config['max_products_per_page']}")
        else:
            self.logger.info(f"📊 页面产品数量: 配置={adjusted_config['max_products_per_page']}, 实际数据未获取")

        return adjusted_config
    
    def calculate_pagination_summary(self, total_products: int, products_per_page: int) -> Dict[str, Any]:
        """
        计算分页摘要信息
        
        Args:
            total_products: 总产品数
            products_per_page: 每页产品数
            
        Returns:
            分页摘要信息
        """
        if products_per_page <= 0:
            raise ValueError("每页产品数必须大于0")
        
        total_pages = (total_products + products_per_page - 1) // products_per_page  # 向上取整
        
        summary = {
            'total_products': total_products,
            'products_per_page': products_per_page,
            'total_pages': total_pages,
            'last_page_products': total_products % products_per_page or products_per_page
        }
        
        self.logger.debug(f"分页摘要: {summary}")
        return summary
