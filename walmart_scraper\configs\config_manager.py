#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from utils.logger.logger import get_logger

class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.config_root = Path(__file__).parent
        self.main_config = self._load_main_config()
        
    def _load_main_config(self) -> Dict[str, Any]:
        """加载主配置文件"""
        try:
            main_config_path = self.config_root / "main_config.json"
            with open(main_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.debug(f"✅ 加载主配置文件: {main_config_path}")
            return config
        except Exception as e:
            self.logger.error(f"❌ 加载主配置文件失败: {e}")
            return {}
    
    def get_module_config(self, module_name: str) -> Dict[str, Any]:
        """
        获取模块配置（包含从工作流迁移的业务配置）

        Args:
            module_name: 模块名称 (category, product_list, product_detail, pagination)

        Returns:
            模块配置字典
        """
        try:
            config_path = self.main_config.get('config_paths', {}).get('modules', {}).get(module_name)
            if not config_path:
                self.logger.warning(f"⚠️  模块 {module_name} 的配置路径未找到")
                return {}

            full_path = self.config_root / config_path
            with open(full_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            self.logger.debug(f"✅ 加载模块配置: {module_name} -> {full_path}")
            return self._filter_comments(config)

        except Exception as e:
            self.logger.error(f"❌ 加载模块配置失败 {module_name}: {e}")
            return {}
    
    def get_api_config(self, api_name: str = "oxylabs") -> Dict[str, Any]:
        """
        获取API配置
        
        Args:
            api_name: API名称 (默认: oxylabs)
            
        Returns:
            API配置字典
        """
        try:
            config_path = self.main_config.get('config_paths', {}).get('api', {}).get(api_name)
            if not config_path:
                self.logger.warning(f"⚠️  API {api_name} 的配置路径未找到")
                return {}
            
            full_path = self.config_root / config_path
            with open(full_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.logger.debug(f"✅ 加载API配置: {api_name} -> {full_path}")
            return self._filter_comments(config)
            
        except Exception as e:
            self.logger.error(f"❌ 加载API配置失败 {api_name}: {e}")
            return {}
    
    def get_rendering_strategies(self) -> Dict[str, Any]:
        """
        获取渲染策略配置
        
        Returns:
            渲染策略配置字典
        """
        try:
            config_path = self.main_config.get('config_paths', {}).get('rendering', {}).get('strategies')
            if not config_path:
                self.logger.warning(f"⚠️  渲染策略配置路径未找到")
                return {}
            
            full_path = self.config_root / config_path
            with open(full_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.logger.debug(f"✅ 加载渲染策略配置: {full_path}")
            return self._filter_comments(config)
            
        except Exception as e:
            self.logger.error(f"❌ 加载渲染策略配置失败: {e}")
            return {}
    
    def get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return self.main_config.get('default_settings', {})
    
    def get_workflow_config(self) -> Dict[str, Any]:
        """获取工作流配置"""
        return self.main_config.get('workflow', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置"""
        return self.main_config.get('output', {})

    def get_workflow_config(self) -> Dict[str, Any]:
        """
        获取工作流配置（仅包含工作流编排相关配置）

        Returns:
            工作流配置字典
        """
        try:
            workflow_config_path = self.config_root / "workflow_config.json"
            with open(workflow_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            self.logger.debug(f"✅ 加载工作流配置: {workflow_config_path}")
            return self._filter_comments(config)

        except Exception as e:
            self.logger.error(f"❌ 加载工作流配置失败: {e}")
            return {}
    
    def _filter_comments(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤配置中的注释字段（以_开头的字段）"""
        if not isinstance(config, dict):
            return config
        
        filtered = {}
        for key, value in config.items():
            if not key.startswith('_'):
                if isinstance(value, dict):
                    filtered[key] = self._filter_comments(value)
                else:
                    filtered[key] = value
        
        return filtered
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        try:
            # 检查主配置
            if not self.main_config:
                self.logger.error("❌ 主配置文件为空")
                return False
            
            # 检查必要的配置路径
            config_paths = self.main_config.get('config_paths', {})
            required_sections = ['modules', 'api', 'rendering']
            
            for section in required_sections:
                if section not in config_paths:
                    self.logger.error(f"❌ 缺少配置路径: {section}")
                    return False
            
            # 检查模块配置文件是否存在
            modules = config_paths.get('modules', {})
            for module_name, config_path in modules.items():
                full_path = self.config_root / config_path
                if not full_path.exists():
                    self.logger.error(f"❌ 模块配置文件不存在: {module_name} -> {full_path}")
                    return False
            
            self.logger.info("✅ 配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 配置验证失败: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()
