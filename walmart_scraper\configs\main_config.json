{"_description": "Walmart抓取器主配置文件", "_version": "3.0.0", "_last_updated": "2025-07-05", "_architecture": "统一配置管理架构", "project": {"name": "walmart_scraper", "version": "3.0.0", "description": "基于新架构的模块化Walmart抓取系统"}, "config_paths": {"_description": "配置文件路径映射", "modules": {"category": "modules/category.json", "product_list": "modules/product_list.json", "product_detail": "modules/product_detail.json", "pagination": "modules/pagination.json"}, "api": {"oxylabs": "api/oxylabs.json"}, "rendering": {"strategies": "rendering/strategies.json"}}, "default_settings": {"_description": "全局默认设置", "timeout_seconds": 60, "max_retries": 3, "retry_delay_seconds": 2, "output_directory": "output", "log_level": "INFO", "enable_debug": false, "max_categories": 2, "max_pages_per_category": 3, "max_products_per_page": 10}, "workflow": {"_description": "工作流配置", "default_strategy": "standard", "enable_progress_tracking": true, "enable_error_recovery": true, "max_concurrent_requests": 1}, "data_validation": {"_description": "数据验证配置", "min_success_rate": 0.8, "required_fields": ["title", "link"], "enable_data_quality_check": true}, "output": {"_description": "输出配置", "format": "json", "include_metadata": true, "include_timestamps": true, "compress_output": false}, "environment": {"_description": "环境配置", "current": "development", "available": ["development", "testing", "production"]}}