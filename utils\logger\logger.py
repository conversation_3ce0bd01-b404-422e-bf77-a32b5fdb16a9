#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器
提供统一的日志配置和管理功能
"""

import os
import json
import logging
import logging.config
from typing import Dict, Any, Optional
from datetime import datetime
import re


class WorkflowLoggerAdapter(logging.LoggerAdapter):
    """工作流日志适配器，自动添加工作流步骤信息"""

    def __init__(self, logger, workflow_step: str = "UNKNOWN"):
        super().__init__(logger, {"workflow_step": workflow_step})
        self.workflow_step = workflow_step

    def process(self, msg, kwargs):
        """处理日志消息，添加工作流步骤信息"""
        # 更新extra字典中的workflow_step
        kwargs.setdefault('extra', {})
        kwargs['extra']['workflow_step'] = self.workflow_step
        return f"[{self.workflow_step}] {msg}", kwargs

    def set_workflow_step(self, step: str):
        """设置当前工作流步骤"""
        self.workflow_step = step
        self.extra['workflow_step'] = step


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化日志管理器
        
        Args:
            config_path: 日志配置文件路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config = self._load_config()
        self._setup_logging()
        self._workflow_adapters = {}
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(current_dir, "logging_config.json")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载日志配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 过滤掉注释字段
            return self._filter_comments(config)
            
        except FileNotFoundError:
            print(f"⚠️  日志配置文件未找到: {self.config_path}")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            print(f"❌ 日志配置文件格式错误: {e}")
            return self._get_default_config()
    
    def _filter_comments(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤配置中的注释字段（以_开头的字段）"""
        if isinstance(config, dict):
            return {
                key: self._filter_comments(value)
                for key, value in config.items()
                if not key.startswith('_')
            }
        elif isinstance(config, list):
            return [self._filter_comments(item) for item in config]
        else:
            return config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "simple": {
                    "format": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": "INFO",
                    "formatter": "simple"
                }
            },
            "root": {
                "level": "INFO",
                "handlers": ["console"]
            }
        }
    
    def _setup_logging(self):
        """设置日志配置"""
        try:
            # 确保日志目录存在
            self._ensure_log_directories()

            # 修复日志文件路径为绝对路径
            self._fix_log_file_paths()

            # 应用日志配置
            logging.config.dictConfig(self.config)

            # 记录日志系统启动
            logger = logging.getLogger("walmart_scraper")
            logger.info("🚀 日志系统初始化完成")

        except Exception as e:
            print(f"❌ 日志系统初始化失败: {e}")
            # 使用基本配置
            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s - %(levelname)s - %(message)s"
            )
    
    def _ensure_log_directories(self):
        """确保日志目录存在"""
        custom_settings = self.config.get("custom_settings", {})
        if custom_settings.get("auto_create_dirs", True):
            log_dir = custom_settings.get("log_directory", "output/logs")

            # 确保使用项目根目录的绝对路径
            if not os.path.isabs(log_dir):
                # 获取项目根目录（utils/logger的上上级目录）
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                log_dir = os.path.join(project_root, log_dir)

            os.makedirs(log_dir, exist_ok=True)

    def _fix_log_file_paths(self):
        """修复日志文件路径为绝对路径"""
        handlers = self.config.get("handlers", {})
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        for handler_name, handler_config in handlers.items():
            if "filename" in handler_config:
                filename = handler_config["filename"]
                if not os.path.isabs(filename):
                    # 转换为绝对路径
                    abs_filename = os.path.join(project_root, filename)
                    handler_config["filename"] = abs_filename

    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        return logging.getLogger(name)
    
    def get_workflow_logger(self, workflow_step: str = "UNKNOWN") -> WorkflowLoggerAdapter:
        """
        获取工作流日志适配器
        
        Args:
            workflow_step: 工作流步骤名称
            
        Returns:
            工作流日志适配器
        """
        if workflow_step not in self._workflow_adapters:
            base_logger = logging.getLogger("walmart_scraper.workflow")
            self._workflow_adapters[workflow_step] = WorkflowLoggerAdapter(
                base_logger, workflow_step
            )
        
        return self._workflow_adapters[workflow_step]
    
    def mask_sensitive_data(self, text: str) -> str:
        """
        屏蔽敏感数据
        
        Args:
            text: 原始文本
            
        Returns:
            屏蔽敏感信息后的文本
        """
        custom_settings = self.config.get("custom_settings", {})
        sensitive_config = custom_settings.get("sensitive_data", {})
        
        if not sensitive_config.get("mask_credentials", True):
            return text
        
        # 屏蔽密码
        text = re.sub(r'("password":\s*")[^"]*(")', r'\1***\2', text)
        text = re.sub(r'(password=)[^&\s]*', r'\1***', text)
        
        # 屏蔽API密钥
        text = re.sub(r'("api_key":\s*")[^"]*(")', r'\1***\2', text)
        text = re.sub(r'(api_key=)[^&\s]*', r'\1***', text)
        
        # 屏蔽用户名（可选）
        if sensitive_config.get("mask_personal_info", True):
            text = re.sub(r'("username":\s*")[^"]*(")', r'\1***\2', text)
        
        return text
    
    def log_performance(self, logger: logging.Logger, operation: str, 
                       duration: float, **kwargs):
        """
        记录性能信息
        
        Args:
            logger: 日志器
            operation: 操作名称
            duration: 耗时（秒）
            **kwargs: 额外的性能数据
        """
        custom_settings = self.config.get("custom_settings", {})
        perf_config = custom_settings.get("performance_logging", {})
        
        if not perf_config.get("enabled", True):
            return
        
        perf_info = f"⏱️  {operation} 耗时: {duration:.2f}秒"
        
        if kwargs:
            extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            perf_info += f" ({extra_info})"
        
        logger.info(perf_info)
    
    def log_request_info(self, logger: logging.Logger, url: str,
                        method: str = "GET", status_code: int = None,
                        duration: float = None):
        """
        记录请求信息

        Args:
            logger: 日志器
            url: 请求URL
            method: 请求方法
            status_code: 响应状态码
            duration: 请求耗时
        """
        from urllib.parse import urlparse
        parsed = urlparse(url)

        # 显示基础URL（域名+路径）
        base_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        request_info = f"🌐 {method} {base_url}"

        if status_code:
            request_info += f" -> {status_code}"

        if duration:
            request_info += f" ({duration:.2f}s)"

        logger.debug(request_info)

        # 如果有查询参数，单独显示（便于调试）
        if parsed.query:
            logger.debug(f"📋 查询参数: {parsed.query}")
    
    def create_operation_logger(self, operation_name: str) -> logging.Logger:
        """
        为特定操作创建专用日志器
        
        Args:
            operation_name: 操作名称
            
        Returns:
            专用日志器
        """
        logger_name = f"walmart_scraper.{operation_name}"
        return logging.getLogger(logger_name)


# 全局日志管理器实例
_logger_manager = None


def get_logger_manager() -> LoggerManager:
    """获取全局日志管理器实例"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    return _logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return get_logger_manager().get_logger(name)


def get_workflow_logger(step: str = "UNKNOWN") -> WorkflowLoggerAdapter:
    """获取工作流日志器的便捷函数"""
    return get_logger_manager().get_workflow_logger(step)


def setup_logging(config_path: str = None):
    """设置日志系统的便捷函数"""
    global _logger_manager
    _logger_manager = LoggerManager(config_path)


# 使用示例
if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 获取普通日志器
    logger = get_logger("walmart_scraper.test")
    logger.info("这是一个测试日志")
    
    # 获取工作流日志器
    workflow_logger = get_workflow_logger("TEST")
    workflow_logger.info("这是一个工作流测试日志")
    
    # 性能日志示例
    import time
    start_time = time.time()
    time.sleep(0.1)  # 模拟操作
    duration = time.time() - start_time
    
    manager = get_logger_manager()
    manager.log_performance(logger, "测试操作", duration, items=100)
