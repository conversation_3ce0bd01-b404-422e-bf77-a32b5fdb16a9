#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品列表抓取器
重构现有的产品抓取功能，专门处理产品列表页面的抓取
"""

import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_module import BaseModule
from ..workflow.workflow_config import WorkflowConfig


class ProductListModule(BaseModule):
    """产品列表模块 - 重构现有产品抓取功能"""

    def __init__(self, config: WorkflowConfig = None):
        """
        初始化产品列表模块

        Args:
            config: 工作流配置
        """
        super().__init__(config, "product_list")
        
        # 产品配置
        self.pagination_config = config.pagination if config else None
        self.product_selectors = self.module_config.get('selectors', {})

        max_products = self.pagination_config.max_products_per_page if self.pagination_config else 50
        self.logger.info(f"📦 产品列表模块初始化: 每页最大产品数={max_products}")
    
    def process(self, page_url: str, **kwargs) -> Dict[str, Any]:
        """
        实现基类的抽象方法

        Args:
            page_url: 页面URL
            **kwargs: 额外参数

        Returns:
            页面抓取结果
        """
        return self.scrape_page(page_url, **kwargs)

    def scrape_page_with_sorting(self, page_url: str, sorting_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        带排序配置的页面抓取

        Args:
            page_url: 页面URL
            sorting_config: 排序配置，包含browser_instructions等

        Returns:
            页面抓取结果，包含产品列表和分页信息
        """
        try:
            # 准备抓取页面
            self.logger.info(f"🌐 准备抓取页面: {page_url}")

            # 合并排序配置到请求参数
            request_params = {}
            if sorting_config:
                request_params.update(sorting_config)
                self.logger.info(f"🔄 应用排序配置到页面抓取")
                self.logger.debug(f"🔧 排序配置内容: {sorting_config}")

            if request_params:
                self.logger.debug(f"🔧 浏览器交互参数: {request_params}")
            else:
                self.logger.debug(f"🔧 浏览器交互参数: 无 (使用URL参数排序)")

            # 发起请求
            response = self.make_request(
                url=page_url,
                parsing_instructions=self.get_product_parsing_instructions(),
                **request_params
            )

            return self._process_response(response, page_url)

        except Exception as e:
            self.logger.error(f"❌ 带排序的页面抓取失败: {e}")
            # 回退到普通抓取
            self.logger.info("🔄 回退到普通页面抓取")
            return self.scrape_page(page_url)

    def scrape_page(self, page_url: str, strategy: str = None) -> Dict[str, Any]:
        """
        抓取单页产品列表
        
        Args:
            page_url: 页面URL
            strategy: 渲染策略
            
        Returns:
            页面产品结果
        """
        self.logger.debug(f"📄 抓取产品页面: {page_url}")
        start_time = time.time()
        
        try:
            # 获取解析指令
            parsing_instructions = self.get_product_parsing_instructions()
            
            # 确定渲染策略
            if strategy is None:
                strategy = "product_list"  # 默认使用产品列表专用策略
            
            # 发送请求
            response = self.retry_request(
                self.make_request,
                url=page_url,
                parsing_instructions=parsing_instructions,
                strategy=strategy
            )
            
            if not response or not self.validate_response(response):
                self.logger.warning(f"⚠️  页面请求失败: {page_url}")
                return self._create_empty_page_result(page_url)
            
            # 提取产品数据
            products = self._extract_products(response, page_url)
            
            # 应用产品数量限制
            limited_products = self._apply_product_limit(products)
            
            execution_time = time.time() - start_time
            
            # 构建页面结果
            page_result = {
                "url": page_url,
                "products": limited_products,
                "products_count": len(limited_products),
                "original_count": len(products),
                "execution_time": execution_time,
                "strategy": strategy,
                "scraped_at": datetime.now().isoformat(),
                "success": True
            }
            
            self.logger.debug(f"✅ 页面抓取完成: {len(limited_products)} 个产品, {execution_time:.2f}秒")
            
            return page_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 页面抓取失败: {e}")
            
            return {
                "url": page_url,
                "products": [],
                "products_count": 0,
                "original_count": 0,
                "execution_time": execution_time,
                "strategy": strategy or "unknown",
                "scraped_at": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def scrape_category_products(self, category_url: str, max_pages: int,
                                strategy: str = None) -> Dict[str, Any]:
        """
        抓取指定分类的所有产品（支持分页）
        
        Args:
            category_url: 分类URL
            max_pages: 最大页数
            strategy: 渲染策略
            
        Returns:
            分类产品结果
        """
        self.logger.info(f"📂 开始抓取分类产品: {category_url}, 最大页数={max_pages}")
        start_time = time.time()
        
        all_products = []
        pages_scraped = 0
        errors = []
        
        try:
            for page_num in range(1, max_pages + 1):
                self.logger.info(f"📄 抓取第 {page_num} 页...")
                
                # 构建页面URL（这里需要分页管理器的支持）
                page_url = self._build_page_url(category_url, page_num)
                
                # 抓取页面
                page_result = self.scrape_page(page_url, strategy)
                
                if not page_result.get('success') or not page_result.get('products'):
                    self.logger.warning(f"⚠️  第 {page_num} 页无产品数据，停止抓取")
                    if page_result.get('error'):
                        errors.append(f"第{page_num}页: {page_result['error']}")
                    break
                
                # 添加页面产品
                page_products = page_result['products']
                for product in page_products:
                    product['page_number'] = page_num
                
                all_products.extend(page_products)
                pages_scraped += 1
                
                self.logger.info(f"✅ 第 {page_num} 页完成: {len(page_products)} 个产品")
                
                # 添加请求间隔
                if page_num < max_pages:
                    delay = self.config.performance.request_delay
                    time.sleep(delay)
            
            execution_time = time.time() - start_time
            
            # 构建分类结果
            category_result = {
                "category_url": category_url,
                "total_products": len(all_products),
                "pages_scraped": pages_scraped,
                "max_pages": max_pages,
                "products": all_products,
                "execution_time": execution_time,
                "strategy": strategy or "ecommerce",
                "scraped_at": datetime.now().isoformat(),
                "success": len(all_products) > 0,
                "errors": errors
            }
            
            self.logger.info(f"✅ 分类抓取完成: {len(all_products)} 个产品, "
                           f"{pages_scraped} 页, {execution_time:.2f}秒")
            
            return category_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 分类抓取失败: {e}")
            
            return {
                "category_url": category_url,
                "total_products": len(all_products),
                "pages_scraped": pages_scraped,
                "max_pages": max_pages,
                "products": all_products,
                "execution_time": execution_time,
                "strategy": strategy or "ecommerce",
                "scraped_at": datetime.now().isoformat(),
                "success": False,
                "errors": errors + [str(e)]
            }
    
    def get_product_parsing_instructions(self) -> Dict[str, Any]:
        """
        获取产品解析指令（包含分页信息解析）

        Returns:
            解析指令
        """
        # 从模块配置中获取产品解析指令
        parsing_instructions = self.module_config.get('parsing_instructions', {})

        # 获取分页解析指令（从pagination.json）
        pagination_parsing = self._get_pagination_parsing_instructions()

        # 合并解析指令
        if parsing_instructions:
            # 过滤掉注释字段
            filtered_instructions = super()._filter_comments(parsing_instructions)

            # 如果product_list.json中有pagination_info，使用pagination.json中的覆盖
            if pagination_parsing and 'pagination_info' in pagination_parsing:
                filtered_instructions['pagination_info'] = pagination_parsing['pagination_info']
                self.logger.debug("✅ 使用pagination.json中的分页解析指令")

            return filtered_instructions

        # 如果配置中没有，使用默认解析指令
        default_instructions = self._get_default_product_parsing_instructions()

        # 添加分页解析指令
        if pagination_parsing and 'pagination_info' in pagination_parsing:
            default_instructions['pagination_info'] = pagination_parsing['pagination_info']

        return default_instructions

    def _get_pagination_parsing_instructions(self) -> Dict[str, Any]:
        """
        从pagination.json获取分页解析指令

        Returns:
            分页解析指令，如果获取失败返回空字典
        """
        try:
            import os
            import json

            # 构建pagination.json路径
            config_path = os.path.join(
                os.path.dirname(__file__),
                '..', 'configs', 'modules', 'pagination.json'
            )

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    pagination_config = json.load(f)

                # 获取解析指令部分
                parsing_instructions = pagination_config.get('parsing_instructions', {})
                if parsing_instructions:
                    self.logger.debug(f"✅ 从pagination.json加载分页解析指令")
                    return super()._filter_comments(parsing_instructions)
                else:
                    self.logger.debug(f"⚠️  pagination.json中未找到parsing_instructions")
                    return {}
            else:
                self.logger.warning(f"⚠️  pagination.json文件不存在: {config_path}")
                return {}

        except Exception as e:
            self.logger.error(f"❌ 加载pagination.json解析指令失败: {e}")
            return {}

    def _get_default_product_parsing_instructions(self) -> Dict[str, Any]:
        """获取默认产品解析指令"""
        return {
            "products": {
                "_fns": [
                    {
                        "_fn": "xpath",
                        "_args": "//div[@data-testid='item-stack']//div[@role='group'][@data-item-id]"
                    }
                ],
                "_items": {
                    "title": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//a[@link-identifier]//span[@class='w_iUH7']/text()"
                            }
                        ]
                    },
                    "link": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//a[@link-identifier]/@href"
                            }
                        ]
                    },
                    "link_identifier": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//a[@link-identifier]/@link-identifier"
                            }
                        ]
                    },
                    "price": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//span[contains(@data-automation-id, 'product-price')]/text()"
                            }
                        ]
                    },
                    "rating": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//span[@data-testid='reviews-section']//span[@class='average-rating']/text()"
                            }
                        ]
                    },
                    "image": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//img[@data-testid='productTileImage']/@src"
                            }
                        ]
                    }
                }
            }
        }
    
    def _extract_products(self, response: Dict[str, Any], page_url: str) -> List[Dict[str, Any]]:
        """
        从API响应中提取产品数据
        
        Args:
            response: API响应
            page_url: 页面URL
            
        Returns:
            产品列表
        """
        try:
            # 提取数据
            content = self.extract_data_from_response(response)
            products_data = content.get('products', [])
            
            if not products_data:
                self.logger.warning(f"⚠️  页面无产品数据: {page_url}")
                return []
            
            # 处理产品数据
            products = []
            base_url = 'https://www.walmart.com'  # 使用固定的基础URL
            
            for i, product_data in enumerate(products_data):
                product_info = self._process_single_product(
                    product_data, i + 1, base_url, page_url
                )
                
                if product_info:
                    products.append(product_info)
            
            self.logger.debug(f"📦 提取了 {len(products)} 个产品")
            return products
            
        except Exception as e:
            self.logger.error(f"❌ 产品数据提取失败: {e}")
            return []
    
    def _process_single_product(self, product_data: Dict[str, Any], 
                               position: int, base_url: str, page_url: str) -> Optional[Dict[str, Any]]:
        """
        处理单个产品数据
        
        Args:
            product_data: 原始产品数据
            position: 位置
            base_url: 基础URL
            page_url: 页面URL
            
        Returns:
            处理后的产品信息
        """
        try:
            # 提取基础信息
            title = product_data.get('title', '').strip()
            link = product_data.get('link', '').strip()
            link_identifier = product_data.get('link_identifier', '').strip()
            price = product_data.get('price', '').strip()
            rating = product_data.get('rating', '').strip()
            image = product_data.get('image', '').strip()
            
            # 验证必需字段
            if not title or not link:
                self.logger.debug(f"⚠️  产品数据不完整: title='{title}', link='{link}'")
                return None
            
            # 构建完整URL
            full_link = self.build_full_url(link, base_url)
            
            # 提取产品ID
            product_id = self._extract_product_id(link, link_identifier)
            
            product_info = {
                "position": position,
                "title": title,
                "link": link,
                "full_link": full_link,
                "link_identifier": link_identifier,
                "product_id": product_id,
                "price": price,
                "rating": rating,
                "image": image,
                "page_url": page_url,
                "processed_at": datetime.now().isoformat()
            }
            
            self.logger.debug(f"📦 处理产品: {title[:50]}...")
            
            return product_info
            
        except Exception as e:
            self.logger.warning(f"⚠️  产品数据处理失败: {e}")
            return None
    
    def _extract_product_id(self, link: str, link_identifier: str) -> str:
        """
        提取产品ID
        
        Args:
            link: 产品链接
            link_identifier: 链接标识符
            
        Returns:
            产品ID
        """
        # 优先使用link_identifier
        if link_identifier:
            return link_identifier
        
        # 从链接中提取ID
        try:
            # 从 /ip/product-name/123456789 格式中提取ID
            if '/ip/' in link:
                parts = link.split('/ip/')
                if len(parts) > 1:
                    id_part = parts[1].split('/')[1] if '/' in parts[1] else parts[1]
                    return id_part.split('?')[0]  # 移除查询参数
        except:
            pass
        
        return ""
    
    def _apply_product_limit(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用产品数量限制
        
        Args:
            products: 产品列表
            
        Returns:
            限制后的产品列表
        """
        max_products = self.pagination_config.max_products_per_page
        
        if len(products) <= max_products:
            return products
        
        limited_products = products[:max_products]
        self.logger.info(f"📦 产品数量限制: {len(products)} -> {len(limited_products)}")
        
        return limited_products
    
    def _build_page_url(self, base_url: str, page_number: int) -> str:
        """
        构建页面URL（简单实现，实际应该使用PaginationManager）
        
        Args:
            base_url: 基础URL
            page_number: 页码
            
        Returns:
            页面URL
        """
        if page_number <= 1:
            return base_url
        
        # 简单的页面URL构建
        separator = "&" if "?" in base_url else "?"
        return f"{base_url}{separator}page={page_number}"
    
    def _create_empty_page_result(self, page_url: str) -> Dict[str, Any]:
        """
        创建空的页面结果
        
        Args:
            page_url: 页面URL
            
        Returns:
            空的页面结果
        """
        return {
            "url": page_url,
            "products": [],
            "products_count": 0,
            "original_count": 0,
            "execution_time": 0,
            "strategy": "unknown",
            "scraped_at": datetime.now().isoformat(),
            "success": False,
            "error": "Request failed"
        }

    def  _process_response(self, response: Dict[str, Any], page_url: str) -> Dict[str, Any]:
        """
        处理API响应并提取产品数据

        Args:
            response: API响应
            page_url: 页面URL

        Returns:
            处理后的页面结果
        """
        start_time = time.time()

        try:
            if not response or not self.validate_response(response):
                self.logger.warning(f"⚠️  响应验证失败: {page_url}")
                return self._create_empty_page_result(page_url)

            # 提取产品数据
            extracted_data = self.extract_data_from_response(response)

            # 打印原始数据统计
            raw_products = extracted_data.get('products', [])
            self.logger.debug(f"📊 API返回原始产品数量: {len(raw_products)}")
            if 'pagination_info' in extracted_data:
                self.logger.debug(f"📄 原始分页数据: {extracted_data['pagination_info']}")

            # 提取分页信息
            pagination_info = {}
            if 'pagination_info' in extracted_data:
                raw_pagination = extracted_data['pagination_info']

                # 处理分页信息，支持fallback
                total_pages = raw_pagination.get('total_pages') or raw_pagination.get('total_pages_fallback')

                # 如果total_pages仍然是None，尝试从fallback中提取数字
                if total_pages is None:
                    fallback_data = raw_pagination.get('total_pages_fallback')
                    if fallback_data and isinstance(fallback_data, list):
                        # 从列表中找到最后一个数字
                        for item in reversed(fallback_data):
                            if isinstance(item, str) and item.isdigit():
                                total_pages = int(item)
                                break

                pagination_info = {
                    "current_page": raw_pagination.get('current_page'),
                    "total_pages": total_pages,
                    "next_page_exists": raw_pagination.get('next_page_exists'),
                    "has_next_page": bool(raw_pagination.get('has_next_page', 0))
                }

                self.logger.debug(f"📄 分页信息: {pagination_info}")

            # 处理产品列表
            products = self._process_products_data(extracted_data, page_url)

            # 详细统计信息
            self.logger.debug(f"🔍 处理后产品数量: {len(products)}")
            self.logger.debug(f"📊 原始API数据 -> 处理后: {len(raw_products)} -> {len(products)}")

            # 应用产品数量限制
            max_products = self.pagination_config.max_products_per_page if self.pagination_config else 50
            original_count = len(products)
            limited_products = products[:max_products] if len(products) > max_products else products

            self.logger.debug(f"📦 最终产品数量: {len(limited_products)} (限制: {max_products})")

            execution_time = time.time() - start_time

            page_result = {
                "url": page_url,
                "products": limited_products,
                "products_count": len(limited_products),
                "original_count": original_count,
                "pagination_info": pagination_info,
                "execution_time": execution_time,
                "strategy": "processed",
                "scraped_at": datetime.now().isoformat(),
                "success": True
            }

            self.logger.debug(f"✅ 响应处理完成: {len(limited_products)} 个产品, {execution_time:.2f}秒")

            return page_result

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 响应处理失败: {e}")

            return {
                "url": page_url,
                "products": [],
                "products_count": 0,
                "original_count": 0,
                "execution_time": execution_time,
                "strategy": "failed",
                "scraped_at": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }

    def _process_products_data(self, products_data: Dict[str, Any], page_url: str) -> List[Dict[str, Any]]:
        """
        处理产品数据

        Args:
            products_data: 从API响应中提取的产品数据
            page_url: 页面URL

        Returns:
            处理后的产品列表
        """
        try:
            products = products_data.get('products', [])

            if not products:
                self.logger.warning(f"⚠️  未找到产品数据: {page_url}")
                return []

            processed_products = []
            seen_products = set()  # 用于去重的集合

            for i, product in enumerate(products):
                try:
                    # 调试：显示产品的所有字段
                    if i < 3:  # 只显示前3个产品的调试信息
                        self.logger.debug(f"🔍 产品 {i+1} 原始数据: {list(product.keys())}")
                        self.logger.debug(f"🔍 产品 {i+1} title字段: {repr(product.get('title'))}")

                    # 处理产品标题 - 安全处理None值，支持fallback
                    title = product.get('title') or product.get('title_fallback') or ''
                    if isinstance(title, str):
                        title = title.strip()
                    else:
                        title = str(title).strip() if title else ''

                    if not title:
                        if i < 3:  # 只显示前3个产品的详细调试信息
                            self.logger.debug(f"⚠️  产品 {i+1} 缺少标题，原始值: title={repr(product.get('title'))}, fallback={repr(product.get('title_fallback'))}")
                        continue

                    # 去重检查 - 使用link_identifier或data_item_id作为唯一键
                    link_identifier = product.get('link_identifier', '').strip() if product.get('link_identifier') else ''
                    data_item_id = product.get('data_item_id', '').strip() if product.get('data_item_id') else ''

                    # 创建去重键
                    dedup_key = link_identifier or data_item_id or title
                    if dedup_key in seen_products:
                        self.logger.debug(f"⚠️  跳过重复产品: {title[:50]}... (key: {dedup_key})")
                        continue

                    seen_products.add(dedup_key)

                    # 处理产品链接 - 安全处理None值
                    link = product.get('link') or ''
                    if isinstance(link, str):
                        link = link.strip()
                    else:
                        link = str(link).strip() if link else ''

                    # 处理跟踪链接，提取真实的产品链接
                    if link and 'sp/track' in link and 'rd=' in link:
                        try:
                            import urllib.parse
                            # 从跟踪链接中提取真实链接
                            parsed = urllib.parse.urlparse(link)
                            query_params = urllib.parse.parse_qs(parsed.query)
                            if 'rd' in query_params:
                                real_url = urllib.parse.unquote(query_params['rd'][0])
                                if '/ip/' in real_url:
                                    # 提取 /ip/ 部分
                                    ip_start = real_url.find('/ip/')
                                    if ip_start != -1:
                                        ip_part = real_url[ip_start:]
                                        # 移除查询参数，只保留路径
                                        if '?' in ip_part:
                                            ip_part = ip_part[:ip_part.find('?')]
                                        link = ip_part
                        except Exception as e:
                            self.logger.debug(f"⚠️  链接处理失败: {e}")

                    # 确保链接格式正确
                    if link and not link.startswith('http') and not link.startswith('/'):
                        link = f"https://www.walmart.com{link}"
                    elif link and link.startswith('/'):
                        # 保持相对路径格式
                        pass

                    # 处理价格信息 - 安全处理None值
                    price_current = product.get('price_current') or ''
                    price_current = price_current.strip() if isinstance(price_current, str) else str(price_current).strip() if price_current else ''

                    price_display = product.get('price_display') or ''
                    price_display = price_display.strip() if isinstance(price_display, str) else str(price_display).strip() if price_display else ''

                    # 处理评分信息 - 安全处理None值
                    rating_value = product.get('rating_value') or ''
                    rating_value = rating_value.strip() if isinstance(rating_value, str) else str(rating_value).strip() if rating_value else ''

                    rating_text = product.get('rating_text') or ''
                    rating_text = rating_text.strip() if isinstance(rating_text, str) else str(rating_text).strip() if rating_text else ''

                    # 安全处理其他字段
                    def safe_strip(value):
                        if value is None:
                            return ''
                        if isinstance(value, str):
                            return value.strip()
                        return str(value).strip() if value else ''

                    # 构建处理后的产品数据
                    processed_product = {
                        "title": title,
                        "link": link,
                        "link_identifier": safe_strip(product.get('link_identifier')),
                        "data_item_id": safe_strip(product.get('data_item_id')),
                        "price_current": price_current,
                        "price_display": price_display,
                        "rating_value": rating_value,
                        "rating_text": rating_text,
                        "image": safe_strip(product.get('image')),
                        "best_seller": safe_strip(product.get('best_seller')),
                        "processed_at": datetime.now().isoformat()
                    }

                    processed_products.append(processed_product)

                except Exception as e:
                    self.logger.warning(f"⚠️  处理产品 {i+1} 时出错: {e}")
                    continue

            self.logger.debug(f"✅ 成功处理 {len(processed_products)}/{len(products)} 个产品")

            return processed_products

        except Exception as e:
            self.logger.error(f"❌ 产品数据处理失败: {e}")
            return []


# 便捷函数
def create_product_list_module(config: WorkflowConfig = None) -> ProductListModule:
    """创建产品列表模块的便捷函数"""
    return ProductListModule(config)
