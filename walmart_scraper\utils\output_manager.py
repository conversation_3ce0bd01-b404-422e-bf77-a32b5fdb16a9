#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出管理器
负责管理和组织所有输出文件，按分类分别保存
"""

import os
import json
import gzip
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 使用全局日志管理器
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_logger
from ..workflow.workflow_config import OutputConfig


class OutputManager:
    """输出管理器 - 按分类组织输出文件"""
    
    def __init__(self, config: OutputConfig):
        """
        初始化输出管理器
        
        Args:
            config: 输出配置
        """
        self.config = config
        self.logger = get_logger("walmart_scraper.output")
        
        # 创建基础输出目录
        base_dir_path = Path(config.base_dir)

        # 确保使用项目根目录的绝对路径
        if not base_dir_path.is_absolute():
            # 获取项目根目录（walmart_scraper的上级目录）
            project_root = Path(__file__).parent.parent.parent
            self.base_dir = project_root / base_dir_path
        else:
            self.base_dir = base_dir_path

        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 时间戳（如果启用）
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") if config.include_timestamp else ""
        
        self.logger.info(f"📁 输出管理器初始化: {self.base_dir}")
    
    def get_category_dir(self, category_name: str) -> Path:
        """
        获取分类输出目录
        
        Args:
            category_name: 分类名称
            
        Returns:
            分类目录路径
        """
        # 清理分类名称，移除特殊字符
        safe_name = self._sanitize_filename(category_name)
        
        if self.config.organization == "by_category":
            category_dir = self.base_dir / safe_name
        else:  # unified
            category_dir = self.base_dir
        
        category_dir.mkdir(parents=True, exist_ok=True)
        return category_dir
    
    def save_category_info(self, category_name: str, category_data: Dict[str, Any]):
        """
        保存分类信息
        
        Args:
            category_name: 分类名称
            category_data: 分类数据
        """
        category_dir = self.get_category_dir(category_name)
        filename = self._build_filename(self.config.file_naming["category_info"])
        filepath = category_dir / filename
        
        self._save_json_file(filepath, category_data)
        self.logger.debug(f"💾 分类信息已保存: {filepath}")
    
    def save_page_result(self, category_name: str, page_num: int, 
                        products: List[Dict[str, Any]]):
        """
        保存页面产品结果
        
        Args:
            category_name: 分类名称
            page_num: 页码
            products: 产品列表
        """
        if not self.config.save_intermediate:
            return
        
        category_dir = self.get_category_dir(category_name)
        filename_template = self.config.file_naming["products"]
        filename = self._build_filename(filename_template.format(page=page_num))
        filepath = category_dir / filename
        
        page_data = {
            "category_name": category_name,
            "page_number": page_num,
            "products_count": len(products),
            "products": products,
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json_file(filepath, page_data)
        self.logger.debug(f"📄 第{page_num}页结果已保存: {filepath}")
    
    def save_product_detail(self, category_name: str, product_id: str, 
                           product_detail: Dict[str, Any]):
        """
        保存产品详情
        
        Args:
            category_name: 分类名称
            product_id: 产品ID
            product_detail: 产品详情数据
        """
        category_dir = self.get_category_dir(category_name)
        
        # 创建产品详情子目录
        details_dir = category_dir / "product_details"
        details_dir.mkdir(exist_ok=True)
        
        filename_template = self.config.file_naming["details"]
        filename = self._build_filename(filename_template.format(product_id=product_id))
        filepath = details_dir / filename
        
        self._save_json_file(filepath, product_detail)
        self.logger.debug(f"🔍 产品详情已保存: {filepath}")
    
    def save_category_result(self, category_result):
        """
        保存分类处理结果
        
        Args:
            category_result: CategoryResult 对象
        """
        category_dir = self.get_category_dir(category_result.category_name)
        filename = self._build_filename(self.config.file_naming["summary"])
        filepath = category_dir / filename
        
        # 转换为可序列化的字典
        result_data = {
            "category_name": category_result.category_name,
            "category_url": category_result.category_url,
            "total_pages": category_result.total_pages,
            "total_products": category_result.total_products,
            "execution_time": category_result.execution_time,
            "success": category_result.success,
            "errors": category_result.errors,
            "products_summary": {
                "count": len(category_result.products),
                "with_links": len([p for p in category_result.products if p.get('link')]),
                "sample_products": category_result.products[:5]  # 前5个产品作为示例
            },
            "product_details_summary": {
                "count": len(category_result.product_details),
                "fields": list(category_result.product_details[0].keys()) if category_result.product_details else []
            },
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json_file(filepath, result_data)
        self.logger.info(f"📊 分类结果已保存: {category_result.category_name} -> {filepath}")
    
    def save_workflow_result(self, workflow_result):
        """
        保存工作流结果
        
        Args:
            workflow_result: WorkflowResult 对象
        """
        filename = self._build_filename(self.config.file_naming["workflow_summary"])
        filepath = self.base_dir / filename
        
        # 转换为可序列化的字典
        result_data = {
            "success": workflow_result.success,
            "total_categories": workflow_result.total_categories,
            "total_products": workflow_result.total_products,
            "total_pages": workflow_result.total_pages,
            "execution_time": workflow_result.execution_time,
            "summary": workflow_result.summary,
            "errors": workflow_result.errors,
            "categories_summary": {
                name: {
                    "success": result.success,
                    "total_products": result.total_products,
                    "total_pages": result.total_pages,
                    "execution_time": result.execution_time,
                    "errors_count": len(result.errors)
                }
                for name, result in workflow_result.categories_results.items()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        self._save_json_file(filepath, result_data)
        self.logger.info(f"🎯 工作流结果已保存: {filepath}")
    
    def save_discovery_result(self, discovery_result: Dict[str, Any]):
        """
        保存分类发现结果
        
        Args:
            discovery_result: 分类发现结果
        """
        filename = self._build_filename("category_discovery.json")
        filepath = self.base_dir / filename
        
        self._save_json_file(filepath, discovery_result)
        self.logger.info(f"🔍 分类发现结果已保存: {filepath}")
    
    def _save_json_file(self, filepath: Path, data: Dict[str, Any]):
        """
        保存JSON文件
        
        Args:
            filepath: 文件路径
            data: 数据
        """
        try:
            if self.config.compress_output:
                # 保存为压缩文件
                filepath = filepath.with_suffix(filepath.suffix + '.gz')
                with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            else:
                # 保存为普通JSON文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            self.logger.error(f"❌ 文件保存失败 {filepath}: {e}")
            raise
    
    def _build_filename(self, template: str) -> str:
        """
        构建文件名
        
        Args:
            template: 文件名模板
            
        Returns:
            实际文件名
        """
        if self.config.include_timestamp and self.timestamp:
            # 在文件扩展名前插入时间戳
            name, ext = os.path.splitext(template)
            return f"{name}_{self.timestamp}{ext}"
        return template
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除特殊字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换特殊字符
        import re
        # 保留字母、数字、空格、连字符和下划线
        sanitized = re.sub(r'[^\w\s\-]', '', filename)
        # 将空格替换为下划线
        sanitized = re.sub(r'\s+', '_', sanitized)
        # 移除多余的下划线
        sanitized = re.sub(r'_+', '_', sanitized)
        # 移除开头和结尾的下划线
        sanitized = sanitized.strip('_')
        
        return sanitized or "unknown"
    
    def get_output_summary(self) -> Dict[str, Any]:
        """
        获取输出摘要信息
        
        Returns:
            输出摘要
        """
        summary = {
            "base_directory": str(self.base_dir),
            "organization": self.config.organization,
            "timestamp_enabled": self.config.include_timestamp,
            "compression_enabled": self.config.compress_output,
            "categories": []
        }
        
        # 扫描分类目录
        if self.config.organization == "by_category":
            for item in self.base_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    category_info = self._scan_category_directory(item)
                    summary["categories"].append(category_info)
        
        return summary
    
    def _scan_category_directory(self, category_dir: Path) -> Dict[str, Any]:
        """
        扫描分类目录
        
        Args:
            category_dir: 分类目录路径
            
        Returns:
            分类目录信息
        """
        info = {
            "name": category_dir.name,
            "path": str(category_dir),
            "files": [],
            "product_details_count": 0
        }
        
        # 扫描文件
        for file_path in category_dir.rglob("*.json*"):
            if file_path.is_file():
                file_info = {
                    "name": file_path.name,
                    "size": file_path.stat().st_size,
                    "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                }
                info["files"].append(file_info)
                
                # 统计产品详情文件
                if "product_details" in str(file_path):
                    info["product_details_count"] += 1
        
        return info
    
    def cleanup_old_files(self, days: int = 7) -> int:
        """
        清理旧文件
        
        Args:
            days: 保留天数
            
        Returns:
            删除的文件数量
        """
        import time
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            for file_path in self.base_dir.rglob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
                    self.logger.debug(f"🗑️  删除旧文件: {file_path}")
            
            self.logger.info(f"🧹 清理完成: 删除了 {deleted_count} 个旧文件")
            
        except Exception as e:
            self.logger.error(f"❌ 文件清理失败: {e}")
        
        return deleted_count


# 便捷函数
def create_output_manager(config: OutputConfig) -> OutputManager:
    """创建输出管理器的便捷函数"""
    return OutputManager(config)
