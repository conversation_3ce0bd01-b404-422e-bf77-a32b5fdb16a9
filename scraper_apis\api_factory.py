#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web Scraper API 工厂
负责创建和管理不同的 API 实例
"""

from typing import Dict, Any, List, Type
from .base_api import BaseScraperAPI
from .oxylabs_api import OxylabsAPI

# API 注册表
API_REGISTRY: Dict[str, Type[BaseScraperAPI]] = {
    "oxylabs": OxylabsAPI,
    # 未来可以添加更多 API
    # "scrapingbee": ScrapingBeeAPI,
    # "brightdata": BrightDataAPI,
    # "scraperapi": ScraperAPIService,
}

# API 配置模板
API_CONFIG_TEMPLATES = {
    "oxylabs": {
        "username": "your_oxylabs_username",
        "password": "your_oxylabs_password",
        "description": "Oxylabs Web Scraper API - 企业级抓取服务",
        "pricing": "按请求计费，支持多种套餐",
        "features": ["JavaScript渲染", "自定义解析", "地理位置", "代理轮换"]
    },
    "scrapingbee": {
        "api_key": "your_scrapingbee_api_key", 
        "description": "ScrapingBee - 简单易用的抓取API",
        "pricing": "按请求计费，有免费额度",
        "features": ["JavaScript渲染", "代理轮换", "无验证码"]
    },
    "brightdata": {
        "username": "your_brightdata_username",
        "password": "your_brightdata_password",
        "description": "Bright Data - 全球最大的代理网络",
        "pricing": "按流量计费，企业级服务",
        "features": ["全球代理", "高成功率", "企业支持"]
    }
}

class APIFactory:
    """API 工厂类"""
    
    @staticmethod
    def create_api(api_type: str, config: Dict[str, Any]) -> BaseScraperAPI:
        """
        创建指定类型的 API 实例
        
        Args:
            api_type: API 类型名称
            config: API 配置
            
        Returns:
            API 实例
            
        Raises:
            ValueError: 不支持的 API 类型
        """
        if api_type not in API_REGISTRY:
            supported = ", ".join(API_REGISTRY.keys())
            raise ValueError(f"不支持的 API 类型: {api_type}。支持的类型: {supported}")
        
        api_class = API_REGISTRY[api_type]
        return api_class(config)
    
    @staticmethod
    def get_supported_apis() -> List[str]:
        """
        获取支持的 API 类型列表
        
        Returns:
            API 类型列表
        """
        return list(API_REGISTRY.keys())
    
    @staticmethod
    def get_api_info(api_type: str) -> Dict[str, Any]:
        """
        获取 API 信息
        
        Args:
            api_type: API 类型
            
        Returns:
            API 信息字典
        """
        if api_type not in API_CONFIG_TEMPLATES:
            return {"error": f"未知的 API 类型: {api_type}"}
        
        template = API_CONFIG_TEMPLATES[api_type].copy()
        
        # 添加是否已实现的信息
        template["implemented"] = api_type in API_REGISTRY
        template["api_type"] = api_type
        
        return template
    
    @staticmethod
    def list_all_apis() -> Dict[str, Dict[str, Any]]:
        """
        列出所有 API 的信息
        
        Returns:
            所有 API 信息字典
        """
        all_apis = {}
        for api_type in API_CONFIG_TEMPLATES:
            all_apis[api_type] = APIFactory.get_api_info(api_type)
        return all_apis
    
    @staticmethod
    def validate_api_config(api_type: str, config: Dict[str, Any]) -> bool:
        """
        验证 API 配置
        
        Args:
            api_type: API 类型
            config: 配置字典
            
        Returns:
            配置是否有效
        """
        try:
            api = APIFactory.create_api(api_type, config)
            return api.validate_config()
        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False
    
    @staticmethod
    def test_api_connection(api_type: str, config: Dict[str, Any]) -> bool:
        """
        测试 API 连接
        
        Args:
            api_type: API 类型
            config: 配置字典
            
        Returns:
            连接是否成功
        """
        try:
            api = APIFactory.create_api(api_type, config)
            return api.test_connection()
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False

def get_supported_apis() -> List[str]:
    """获取支持的 API 列表（便捷函数）"""
    return APIFactory.get_supported_apis()

def print_api_comparison():
    """打印 API 对比信息"""
    print("🔧 支持的 Web Scraper APIs:")
    print("=" * 60)
    
    all_apis = APIFactory.list_all_apis()
    
    for api_type, info in all_apis.items():
        status = "✅ 已实现" if info["implemented"] else "🚧 计划中"
        print(f"\n📌 {api_type.upper()} {status}")
        print(f"   描述: {info['description']}")
        print(f"   定价: {info['pricing']}")
        print(f"   功能: {', '.join(info['features'])}")

if __name__ == "__main__":
    # 演示用法
    print_api_comparison()
    
    print(f"\n🧪 测试 API 工厂:")
    supported = get_supported_apis()
    print(f"   支持的 APIs: {supported}")
    
    # 测试创建 Oxylabs API（需要有效配置）
    test_config = {
        "username": "test_user",
        "password": "test_pass"
    }
    
    try:
        api = APIFactory.create_api("oxylabs", test_config)
        print(f"   ✅ 成功创建 {api.name} API 实例")
        print(f"   支持功能: {api.get_supported_features()}")
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")
