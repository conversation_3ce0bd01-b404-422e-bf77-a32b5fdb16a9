#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品详情抓取器
通用模块，用于抓取单个产品详情页的信息，支持按需调用
"""

import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_scraper import BaseScraper
from ..workflow.workflow_config import WorkflowConfig


class ProductDetailScraper(BaseScraper):
    """产品详情抓取器 - 通用模块，按需调用"""
    
    def __init__(self, config: WorkflowConfig):
        """
        初始化产品详情抓取器
        
        Args:
            config: 工作流配置
        """
        super().__init__(config, "product_detail")
        
        # 产品详情配置
        self.detail_config = config.product_details
        self.detail_selectors = self.scraper_config.get('product_detail', {})
        
        self.logger.info(f"🔍 产品详情抓取器初始化: 启用={self.detail_config.enabled}")
    
    def scrape(self, product_url: str, product_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        抓取单个产品详情
        
        Args:
            product_url: 产品详情页URL
            product_info: 基础产品信息（可选）
            
        Returns:
            产品详情数据
        """
        return self.scrape_product_detail(product_url, product_info)
    
    def scrape_product_detail(self, product_url: str, 
                             product_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        抓取单个产品详情
        
        Args:
            product_url: 产品详情页URL
            product_info: 基础产品信息（可选）
            
        Returns:
            产品详情数据
        """
        if not self.detail_config.enabled:
            self.logger.debug("🚫 产品详情抓取未启用")
            return {}
        
        self.logger.debug(f"🔍 开始抓取产品详情: {product_url}")
        start_time = time.time()
        
        try:
            # 构建完整URL
            full_url = self.build_full_url(product_url)
            
            # 获取解析指令
            parsing_instructions = self.get_detail_parsing_instructions()
            
            # 发送请求
            response = self.retry_request(
                self.make_request,
                url=full_url,
                parsing_instructions=parsing_instructions,
                strategy="ecommerce"  # 使用电商策略
            )
            
            if not response or not self.validate_response(response):
                self.logger.warning(f"⚠️  产品详情请求失败: {product_url}")
                return self._create_empty_detail(product_url, product_info)
            
            # 提取产品详情数据
            detail_data = self.extract_data_from_response(response)
            
            # 处理和清理数据
            processed_detail = self._process_product_detail(
                detail_data, product_url, product_info
            )
            
            execution_time = time.time() - start_time
            self.logger.debug(f"✅ 产品详情抓取完成: {execution_time:.2f}秒")
            
            return processed_detail
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 产品详情抓取失败: {e}")
            return self._create_empty_detail(product_url, product_info, str(e))
    
    def batch_scrape_details(self, category_name: str, 
                           products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量抓取产品详情
        
        Args:
            category_name: 分类名称
            products: 产品列表
            
        Returns:
            产品详情列表
        """
        if not self.detail_config.enabled:
            self.logger.debug("🚫 产品详情抓取未启用")
            return []
        
        self.logger.info(f"📦 开始批量抓取产品详情: {category_name}, {len(products)} 个产品")
        
        details = []
        batch_size = self.detail_config.batch_size
        delay = self.detail_config.delay_between_details
        
        # 分批处理
        for i in range(0, len(products), batch_size):
            batch = products[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(products) + batch_size - 1) // batch_size
            
            self.logger.info(f"📦 处理批次 {batch_num}/{total_batches}: {len(batch)} 个产品")
            
            # 处理当前批次
            for j, product in enumerate(batch):
                product_url = product.get('link', '')
                if not product_url:
                    self.logger.warning(f"⚠️  产品缺少链接: {product.get('title', 'Unknown')}")
                    continue
                
                self.logger.debug(f"🔍 抓取产品 {i + j + 1}/{len(products)}: {product.get('title', '')[:50]}...")
                
                # 抓取产品详情
                detail = self.scrape_product_detail(product_url, product)
                if detail:
                    details.append(detail)
                
                # 添加延迟（除了批次内最后一个产品）
                if j < len(batch) - 1:
                    time.sleep(delay)
            
            # 批次间延迟
            if i + batch_size < len(products):
                self.logger.debug(f"⏱️  批次间等待 {delay * 2} 秒...")
                time.sleep(delay * 2)
        
        self.logger.info(f"✅ 批量抓取完成: 成功获取 {len(details)} 个产品详情")
        return details
    
    def get_detail_parsing_instructions(self) -> Dict[str, Any]:
        """
        获取产品详情解析指令
        
        Returns:
            解析指令
        """
        # 从配置中获取解析指令模板
        parsing_template = self.detail_selectors.get('parsing_instructions', {})

        if parsing_template:
            # 过滤掉注释字段
            return super()._filter_comments(parsing_template)
        
        # 如果配置中没有，使用默认解析指令
        return self._get_default_parsing_instructions()
    
    def _get_default_parsing_instructions(self) -> Dict[str, Any]:
        """获取默认解析指令"""
        return {
            "product_detail": {
                "title": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//h1[@data-automation-id='product-title']/text()"
                        }
                    ]
                },
                "price": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//span[@itemprop='price']/text()"
                        }
                    ]
                },
                "rating": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//span[@data-testid='reviews-section-stars']/@aria-label"
                        }
                    ]
                },
                "description": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//div[@data-testid='product-description']//text()"
                        }
                    ]
                },
                "images": {
                    "_fns": [
                        {
                            "_fn": "xpath",
                            "_args": "//img[@data-testid='hero-image-container']/@src"
                        }
                    ]
                }
            }
        }
    
    def _process_product_detail(self, detail_data: Dict[str, Any], 
                               product_url: str, 
                               product_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理和清理产品详情数据
        
        Args:
            detail_data: 原始详情数据
            product_url: 产品URL
            product_info: 基础产品信息
            
        Returns:
            处理后的产品详情
        """
        # 提取产品详情
        product_detail = detail_data.get('product_detail', {})
        
        # 构建最终的产品详情对象
        processed_detail = {
            "url": product_url,
            "scraped_at": datetime.now().isoformat(),
            "scraper_version": "1.0.0"
        }
        
        # 添加基础产品信息（如果提供）
        if product_info:
            processed_detail.update({
                "list_title": product_info.get('title', ''),
                "list_link_identifier": product_info.get('link_identifier', ''),
                "list_price": product_info.get('price', ''),
                "list_rating": product_info.get('rating', '')
            })
        
        # 处理详情字段
        for field in self.detail_config.detail_fields:
            value = product_detail.get(field, '')
            
            # 清理和格式化数据
            if field == 'price':
                processed_detail[field] = self._clean_price(value)
            elif field == 'rating':
                processed_detail[field] = self._clean_rating(value)
            elif field == 'images':
                processed_detail[field] = self._clean_images(value)
            elif field == 'description':
                processed_detail[field] = self._clean_text(value)
            else:
                processed_detail[field] = self._clean_text(value) if isinstance(value, str) else value
        
        return processed_detail
    
    def _clean_price(self, price_text: str) -> Dict[str, Any]:
        """清理价格数据"""
        if not price_text:
            return {"raw": "", "value": None, "currency": "USD"}
        
        import re
        # 提取数字
        price_match = re.search(r'[\d,]+\.?\d*', str(price_text))
        price_value = None
        
        if price_match:
            try:
                price_value = float(price_match.group().replace(',', ''))
            except ValueError:
                pass
        
        return {
            "raw": str(price_text).strip(),
            "value": price_value,
            "currency": "USD"
        }
    
    def _clean_rating(self, rating_text: str) -> Dict[str, Any]:
        """清理评分数据"""
        if not rating_text:
            return {"raw": "", "value": None, "max": 5}
        
        import re
        # 提取评分数字
        rating_match = re.search(r'(\d+\.?\d*)\s*out\s*of\s*(\d+)', str(rating_text))
        
        if rating_match:
            try:
                value = float(rating_match.group(1))
                max_rating = int(rating_match.group(2))
                return {
                    "raw": str(rating_text).strip(),
                    "value": value,
                    "max": max_rating
                }
            except ValueError:
                pass
        
        return {
            "raw": str(rating_text).strip(),
            "value": None,
            "max": 5
        }
    
    def _clean_images(self, images_data: Any) -> List[str]:
        """清理图片数据"""
        if not images_data:
            return []
        
        if isinstance(images_data, str):
            return [images_data] if images_data.strip() else []
        elif isinstance(images_data, list):
            return [img for img in images_data if isinstance(img, str) and img.strip()]
        else:
            return []
    
    def _clean_text(self, text: str) -> str:
        """清理文本数据"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        import re
        cleaned = re.sub(r'\s+', ' ', str(text))
        return cleaned.strip()
    
    def _create_empty_detail(self, product_url: str, 
                           product_info: Dict[str, Any] = None,
                           error: str = None) -> Dict[str, Any]:
        """
        创建空的产品详情对象
        
        Args:
            product_url: 产品URL
            product_info: 基础产品信息
            error: 错误信息
            
        Returns:
            空的产品详情对象
        """
        detail = {
            "url": product_url,
            "scraped_at": datetime.now().isoformat(),
            "scraper_version": "1.0.0",
            "success": False
        }
        
        if product_info:
            detail.update({
                "list_title": product_info.get('title', ''),
                "list_link_identifier": product_info.get('link_identifier', '')
            })
        
        if error:
            detail["error"] = error
        
        # 添加空的详情字段
        for field in self.detail_config.detail_fields:
            detail[field] = "" if field not in ["images"] else []
        
        return detail


# 便捷函数
def create_product_detail_scraper(config: WorkflowConfig) -> ProductDetailScraper:
    """创建产品详情抓取器的便捷函数"""
    return ProductDetailScraper(config)
