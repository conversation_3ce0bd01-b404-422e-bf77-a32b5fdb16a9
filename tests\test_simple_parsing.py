#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单解析测试 - 逐步调试解析器
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scraper_apis.oxylabs_api import OxylabsAPI
from utils.logger.logger import get_logger

def test_simple_parsing():
    """测试简单解析"""
    logger = get_logger("SimpleParsingTest")
    
    # 初始化API
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             'scraper_apis', 'api_config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    api = OxylabsAPI(config['oxylabs'])
    
    url = "https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_1985043_4929045?sort=best_seller&affinityOverride=default"
    
    # 测试1：最简单的解析 - 只查找div元素
    logger.info("🧪 测试1：查找所有div元素")
    simple_parsing = {
        "all_divs": {
            "_fns": [
                {"_fn": "css", "_args": ["div"]}
            ]
        }
    }
    
    response1 = api.scrape_with_parsing(url, simple_parsing)
    if response1 and 'results' in response1:
        result = response1['results'][0]
        parsed = result.get('results', {})
        div_count = len(parsed.get('all_divs', []))
        logger.info(f"  找到 {div_count} 个div元素")
    
    # 测试2：查找data-item-id属性
    logger.info("🧪 测试2：查找data-item-id元素")
    item_parsing = {
        "items": {
            "_fns": [
                {"_fn": "css", "_args": ["[data-item-id]"]}
            ]
        }
    }
    
    response2 = api.scrape_with_parsing(url, item_parsing)
    if response2 and 'results' in response2:
        result = response2['results'][0]
        parsed = result.get('results', {})
        item_count = len(parsed.get('items', []))
        logger.info(f"  找到 {item_count} 个data-item-id元素")
    
    # 测试3：查找role=group元素
    logger.info("🧪 测试3：查找role=group元素")
    group_parsing = {
        "groups": {
            "_fns": [
                {"_fn": "css", "_args": ["[role='group']"]}
            ]
        }
    }
    
    response3 = api.scrape_with_parsing(url, group_parsing)
    if response3 and 'results' in response3:
        result = response3['results'][0]
        parsed = result.get('results', {})
        group_count = len(parsed.get('groups', []))
        logger.info(f"  找到 {group_count} 个role=group元素")
    
    # 测试4：组合选择器
    logger.info("🧪 测试4：组合选择器 div[role='group'][data-item-id]")
    combined_parsing = {
        "products": {
            "_fns": [
                {"_fn": "css", "_args": ["div[role='group'][data-item-id]"]}
            ]
        }
    }
    
    response4 = api.scrape_with_parsing(url, combined_parsing)
    if response4 and 'results' in response4:
        result = response4['results'][0]
        parsed = result.get('results', {})
        product_count = len(parsed.get('products', []))
        logger.info(f"  找到 {product_count} 个产品元素")
        
        # 如果找到产品，显示第一个
        products = parsed.get('products', [])
        if products:
            logger.info(f"  第一个产品HTML: {str(products[0])[:200]}...")
    
    # 测试5：使用xpath
    logger.info("🧪 测试5：使用xpath查找")
    xpath_parsing = {
        "products_xpath": {
            "_fns": [
                {"_fn": "xpath", "_args": ["//div[@role='group'][@data-item-id]"]}
            ]
        }
    }
    
    response5 = api.scrape_with_parsing(url, xpath_parsing)
    if response5 and 'results' in response5:
        result = response5['results'][0]
        parsed = result.get('results', {})
        xpath_count = len(parsed.get('products_xpath', []))
        logger.info(f"  XPath找到 {xpath_count} 个产品元素")

if __name__ == "__main__":
    print("🧪 简单解析测试")
    print("=" * 50)
    test_simple_parsing()
    print("\n🎯 测试完成！")
