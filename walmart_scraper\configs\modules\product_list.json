{"_description": "产品列表模块配置文件", "_version": "1.0.0", "_last_updated": "2025-01-04", "sorting": {"_description": "排序相关配置", "enabled": true, "_enabled_description": "是否启用排序功能", "method": "url_param", "_method_description": "排序方法: url_param(URL参数) 或 browser_interaction(浏览器交互)", "_method_options": ["url_param", "browser_interaction"], "options": {"_description": "可用的排序选项配置", "best_match": {"display_name": "Best Match", "url_param": "sort", "url_value": "best_match", "selector": "input[id='best_match']", "value": "Best Match"}, "price_low": {"display_name": "Price Low", "url_param": "sort", "url_value": "price_low", "selector": "input[id='price_low']", "value": "Price Low"}, "price_high": {"display_name": "Price High", "url_param": "sort", "url_value": "price_high", "selector": "input[id='price_high']", "value": "Price High"}, "best_seller": {"display_name": "Best Seller", "url_param": "sort", "url_value": "best_seller", "selector": "input[id='best_seller']", "value": "Best Seller"}}, "_options_description": "排序选项配置，selector为元素选择器，value为选项值", "default_sort": "best_seller", "_default_sort_description": "默认排序方式，必须是options中的一个key", "interaction_config": {"_description": "浏览器交互配置", "sort_button_selector": "button[aria-label*='Sort by']", "_sort_button_selector_description": "排序按钮的CSS选择器", "sort_options_container": "div[id*='react-aria-']", "_sort_options_container_description": "排序选项容器的CSS选择器", "wait_timeout": 10000, "_wait_timeout_description": "等待超时时间(毫秒)", "network_idle_timeout": 8000, "_network_idle_timeout_description": "网络空闲等待时间(毫秒)"}, "filters": [], "_filters_description": "额外的过滤器配置，格式: [{\"param\": \"filter_name\", \"value\": \"filter_value\"}]"}, "_sorting_extended_description": "从工作流配置迁移的排序业务配置", "selectors": {"_description": "产品列表页面选择器配置", "container": "div[@data-testid='item-stack'] div[@role='group'][@data-item-id]", "title": "a[@link-identifier] span[@class='w_iUH7']", "link": "a[@link-identifier]", "link_identifier": "a[@link-identifier]", "price": "span[data-automation-id='product-price']", "rating": "span[data-testid='reviews-section']", "image": "img[data-testid='productTileImage']"}, "_selectors_description": "用于提取产品信息的选择器", "parsing_instructions": {"_description": "Oxylabs 产品列表解析指令模板", "products": {"_fns": [{"_fn": "css", "_args": ["div[data-testid='item-stack'] div[role='group'][data-item-id]"]}], "_items": {"title": {"_fns": [{"_fn": "css_one", "_args": ["span[data-automation-id='product-title']"]}, {"_fn": "element_text"}]}, "title_fallback": {"_fns": [{"_fn": "css_one", "_args": ["a[link-identifier] span.w_iUH7"]}, {"_fn": "element_text"}]}, "link": {"_fns": [{"_fn": "css_one", "_args": ["a[link-identifier]"]}, {"_fn": "xpath_one", "_args": "./@href"}]}, "link_identifier": {"_fns": [{"_fn": "xpath_one", "_args": ".//a[@link-identifier]/@link-identifier"}]}, "data_item_id": {"_fns": [{"_fn": "xpath_one", "_args": "./@data-item-id"}]}, "price_current": {"_fns": [{"_fn": "xpath_one", "_args": ".//span[@class='w_iUH7'][contains(text(), 'current price')]/text()"}]}, "price_display": {"_fns": [{"_fn": "xpath_one", "_args": ".//div[@data-automation-id='product-price']//text()[normalize-space()]"}]}, "rating_value": {"_fns": [{"_fn": "xpath_one", "_args": ".//span[@data-testid='product-reviews']/@data-value"}]}, "rating_text": {"_fns": [{"_fn": "xpath_one", "_args": ".//span[@class='w_iUH7'][contains(text(), 'out of 5 Stars')]/text()"}]}, "image": {"_fns": [{"_fn": "xpath_one", "_args": ".//img[@data-testid='productTileImage']/@src"}]}, "best_seller": {"_fns": [{"_fn": "xpath_one", "_args": ".//span[contains(text(), 'Best seller')]/text()"}]}}}, "_pagination_info_note": "分页信息解析已迁移到pagination.json中，此处保留注释作为参考"}, "_parsing_instructions_description": "Oxylabs 自定义解析器指令，用于结构化提取产品数据", "limits": {"_description": "产品列表限制配置", "max_products_per_page": 50, "min_products_per_page": 1, "default_products_per_page": 24}, "_limits_description": "产品数量限制配置", "validation": {"_description": "产品数据验证配置", "required_fields": ["title", "link"], "optional_fields": ["price", "rating", "image", "link_identifier"]}, "_validation_description": "产品数据的验证规则", "rendering_strategies": {"_description": "渲染策略配置", "strategies": {"standard": {"_description": "标准渲染策略", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 10}, {"type": "scroll", "x": 0, "y": 2000}]}, "enhanced": {"_description": "增强渲染策略", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 15}, {"type": "scroll", "x": 0, "y": 3000}, {"type": "wait", "wait_time_s": 5}]}, "with_best_seller_sort": {"_description": "Best Seller排序策略 - 修复后的配置", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "div[data-testid='item-stack']"}, "timeout_s": 10}, {"type": "click", "selector": {"type": "css", "value": "button[aria-label*='Sort by']"}}, {"type": "wait", "wait_time_s": 3}, {"type": "click", "selector": {"type": "css", "value": "#best_seller"}}, {"type": "wait", "wait_time_s": 10}]}, "_with_pagination_wait_note": "分页等待策略已迁移到pagination.json中"}}, "_rendering_strategies_description": "产品列表页面的渲染策略配置"}