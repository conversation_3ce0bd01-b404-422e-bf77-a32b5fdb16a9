{"_description": "产品详情模块配置文件", "_version": "1.0.0", "_last_updated": "2025-01-04", "enabled": false, "_enabled_description": "是否启用产品详情抓取（通用模块，按需调用）", "mode": "sample", "_mode_description": "详情抓取模式: all(所有产品), sample(抽样), none(不抓取)", "_mode_options": ["all", "sample", "none"], "sample_size": 10, "_sample_size_description": "抽样模式下每个分类抓取的产品详情数量", "sample_strategy": "random", "_sample_strategy_description": "抽样策略: random(随机), first(前N个), last(后N个)", "_sample_strategy_options": ["random", "first", "last"], "detail_fields": ["title", "price", "rating", "images", "description", "specifications", "reviews_summary"], "_detail_fields_description": "需要抓取的产品详情字段列表", "batch_size": 5, "_batch_size_description": "批量处理产品详情的批次大小", "delay_between_details": 2, "_delay_between_details_description": "抓取产品详情之间的延迟时间（秒）", "selectors": {"_description": "产品详情页选择器配置", "title": "h1[data-automation-id='product-title']", "price": "span[itemprop='price']", "rating": "span[data-testid='reviews-section-stars']", "description": "div[data-testid='product-description']", "images": "img[data-testid='hero-image-container']", "specifications": "div[data-testid='product-specifications']", "reviews": "div[data-testid='reviews-section']"}, "_selectors_description": "用于提取产品详情的选择器", "parsing_instructions": {"_description": "产品详情页解析指令模板", "product_detail": {"title": {"_fns": [{"_fn": "xpath_one", "_args": "//h1[@data-automation-id='product-title']/text()"}]}, "price": {"_fns": [{"_fn": "xpath_one", "_args": "//span[@itemprop='price']/text()"}]}, "rating": {"_fns": [{"_fn": "xpath_one", "_args": "//span[@data-testid='reviews-section-stars']/@aria-label"}]}, "description": {"_fns": [{"_fn": "xpath_one", "_args": "//div[@data-testid='product-description']//text()"}]}, "images": {"_fns": [{"_fn": "xpath", "_args": "//img[@data-testid='hero-image-container']/@src"}]}}}, "_parsing_instructions_description": "产品详情页的解析指令配置", "rendering_strategies": {"_description": "渲染策略配置", "strategies": {"standard": {"_description": "标准渲染策略", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "h1[data-automation-id='product-title']"}, "timeout_s": 10}]}, "enhanced": {"_description": "增强渲染策略", "browser_instructions": [{"type": "wait_for_element", "selector": {"type": "css", "value": "h1[data-automation-id='product-title']"}, "timeout_s": 15}, {"type": "scroll", "x": 0, "y": 2000}, {"type": "wait", "wait_time_s": 3}]}}}, "_rendering_strategies_description": "产品详情页的渲染策略配置"}