#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础模块
提供所有功能模块的通用功能和接口
"""

import json
import time
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_logger, get_logger_manager
from scraper_apis.api_factory import APIFactory
from scraper_apis.base_api import BaseScraperAPI
from ..workflow.workflow_config import WorkflowConfig


class BaseModule(ABC):
    """基础模块抽象类"""

    def __init__(self, config: WorkflowConfig = None, module_name: str = "base"):
        """
        初始化基础模块

        Args:
            config: 工作流配置
            module_name: 模块名称
        """
        self.config = config
        self.module_name = module_name
        self.logger = get_logger(f"walmart_scraper.modules.{module_name}")
        self.logger_manager = get_logger_manager()

        # 加载模块配置
        self.module_config = self._load_module_config()

        # 初始化HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

        # 使用 scraper_apis 工厂模式初始化API
        self.api_config = self._load_api_config()
        self.api_instance = self._create_api_instance()

        # 保持向后兼容的属性（用于现有代码）
        oxylabs_config = self.api_config.get('oxylabs', {})
        self.api_url = oxylabs_config.get('api_url')
        self.username = oxylabs_config.get('username')
        self.password = oxylabs_config.get('password')

        self.logger.debug(f"🔧 {module_name} 模块初始化完成")
    
    def _load_module_config(self) -> Dict[str, Any]:
        """加载模块配置"""
        try:
            import os
            from pathlib import Path

            # 尝试加载模块特定的配置文件（新的统一路径）
            config_path = Path(__file__).parent.parent / "configs" / "modules" / f"{self.module_name}.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 过滤注释字段
                return self._filter_comments(config_data)
            else:
                self.logger.warning(f"⚠️  模块配置文件未找到: {config_path}")
                return {}

        except Exception as e:
            self.logger.warning(f"⚠️  模块配置加载失败，使用默认配置: {e}")
            return {}

    def _load_api_config(self) -> Dict[str, Any]:
        """从 scraper_apis 加载API配置"""
        try:
            from pathlib import Path

            # 从 scraper_apis 目录加载API配置
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "scraper_apis" / "api_config.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.logger.debug(f"✅ 从 scraper_apis 加载API配置: {config_path}")
                return self._filter_comments(config_data)
            else:
                self.logger.error(f"❌ scraper_apis 配置文件未找到: {config_path}")
                raise FileNotFoundError(f"API配置文件不存在: {config_path}")

        except Exception as e:
            self.logger.error(f"❌ API配置加载失败: {e}")
            raise RuntimeError(f"无法加载API配置，请检查 scraper_apis/api_config.json 文件: {e}")

    def _create_api_instance(self) -> BaseScraperAPI:
        """创建API实例"""
        try:
            # 获取默认API类型
            api_type = self.api_config.get('default_api', 'oxylabs')

            # 获取API特定配置
            api_specific_config = self.api_config.get(api_type, {})

            # 创建API实例
            api_instance = APIFactory.create_api(api_type, api_specific_config)

            self.logger.debug(f"✅ 创建 {api_type} API 实例成功")
            return api_instance

        except Exception as e:
            self.logger.error(f"❌ 创建API实例失败: {e}")
            # 返回一个默认的API实例或抛出异常
            raise RuntimeError(f"无法创建API实例: {e}")

    def _filter_comments(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤配置中的注释字段（以_开头但不是解析指令的字段）"""
        if isinstance(config, dict):
            # 特殊处理：保留解析指令中的 _fns, _items, _fn, _args 等字段
            parsing_instruction_fields = {'_fns', '_items', '_fn', '_args'}

            return {
                key: self._filter_comments(value)
                for key, value in config.items()
                if not key.startswith('_') or key in parsing_instruction_fields
            }
        elif isinstance(config, list):
            return [self._filter_comments(item) for item in config]
        else:
            return config
    

    def make_request(self, url: str, parsing_instructions: Dict[str, Any] = None,
                    strategy: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        发送API请求（使用 scraper_apis 架构）

        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            strategy: 渲染策略
            **kwargs: 额外参数

        Returns:
            API响应结果
        """
        start_time = time.time()

        try:
            # 记录请求信息
            self.logger_manager.log_request_info(
                self.logger, url, "POST"
            )

            # 准备API调用参数
            api_kwargs = self._prepare_api_kwargs(url, strategy, **kwargs)

            # 使用 scraper_apis 发送请求
            if parsing_instructions:
                response_data = self.api_instance.scrape_with_parsing(
                    url=url,
                    parsing_instructions=parsing_instructions,
                    **api_kwargs
                )
            else:
                response_data = self.api_instance.scrape(
                    url=url,
                    **api_kwargs
                )

            duration = time.time() - start_time

            # 记录性能信息（scraper_apis 返回的是处理后的数据）
            self.logger_manager.log_performance(
                self.logger, f"API请求 {self.module_name}", duration,
                status_code=200 if response_data else 500
            )

            if response_data:
                self.logger.debug(f"✅ API请求成功")
                return response_data
            else:
                self.logger.error(f"❌ API返回空数据")
                return None

        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"❌ API请求失败: {e}")

            # 记录失败的请求
            self.logger_manager.log_performance(
                self.logger, f"API请求失败 {self.module_name}", duration,
                error=str(e)
            )

            return None

    def _prepare_api_kwargs(self, url: str, strategy: str = None, **kwargs) -> Dict[str, Any]:
        """
        准备API调用参数

        Args:
            url: 目标URL
            strategy: 渲染策略
            **kwargs: 额外参数

        Returns:
            API调用参数
        """
        # 从API配置获取默认参数
        api_kwargs = self.api_config.get('oxylabs', {}).get('default_params', {}).copy()

        # Walmart特殊本地化处理
        if "walmart.com" in url:
            # 移除通用的geo_location，使用Walmart专用参数
            if "geo_location" in api_kwargs:
                del api_kwargs["geo_location"]

            # 添加Walmart本地化参数
            api_kwargs.update({
                "delivery_zip": "10001",  # 纽约ZIP码
                "locale": "en-US"
            })

        # 添加渲染策略
        if strategy:
            strategy_config = self._get_strategy_config(strategy)
            if strategy_config:
                api_kwargs.update(strategy_config)

        # 添加额外参数
        api_kwargs.update(kwargs)

        return api_kwargs

    def _build_request_payload(self, url: str, parsing_instructions: Dict[str, Any] = None,
                              strategy: str = None, **kwargs) -> Dict[str, Any]:
        """
        构建请求载荷
        
        Args:
            url: 目标URL
            parsing_instructions: 解析指令
            strategy: 渲染策略
            **kwargs: 额外参数
            
        Returns:
            请求载荷
        """
        # 基础参数
        payload = {
            "url": url,
            **self.api_config.get('oxylabs', {}).get('default_params', {})
        }

        # Walmart特殊本地化处理
        if "walmart.com" in url:
            # 移除通用的geo_location，使用Walmart专用参数
            if "geo_location" in payload:
                del payload["geo_location"]

            # 添加Walmart本地化参数
            payload.update({
                "delivery_zip": "10001",  # 纽约ZIP码
                "locale": "en-US"
            })

        # 添加解析指令
        if parsing_instructions:
            payload["parse"] = True
            payload["parsing_instructions"] = parsing_instructions

        # 添加渲染策略
        if strategy:
            strategy_config = self._get_strategy_config(strategy)
            if strategy_config:
                payload.update(strategy_config)

        # 添加额外参数
        payload.update(kwargs)

        # 打印最终请求载荷（用于调试）
        self.logger.info(f"🔍 最终发送到Oxylabs的请求载荷:")
        self.logger.info(f"   URL: {payload.get('url')}")
        if 'browser_instructions' in payload:
            self.logger.info(f"   Browser Instructions: {len(payload['browser_instructions'])} 条指令")
            for i, instruction in enumerate(payload['browser_instructions']):
                self.logger.info(f"     {i+1}. {instruction.get('type', 'unknown')}: {instruction}")
        else:
            self.logger.info(f"   Browser Instructions: 无")

        return payload
    
    def _get_strategy_config(self, strategy: str) -> Dict[str, Any]:
        """
        获取渲染策略配置

        Args:
            strategy: 策略名称

        Returns:
            策略配置
        """
        # 首先尝试从模块配置中获取策略
        strategies = self.module_config.get('rendering_strategies', {}).get('strategies', {})
        strategy_config = strategies.get(strategy, {})

        # 如果模块配置中没有，从全局策略配置文件加载
        if not strategy_config:
            strategy_config = self._load_global_strategy_config(strategy)

        if 'browser_instructions' in strategy_config:
            self.logger.debug(f"✅ 找到策略配置: {strategy}")
            return {
                "browser_instructions": strategy_config['browser_instructions'],
                "render": strategy_config.get("render", "html"),
                "timeout_s": strategy_config.get("timeout_s", 30)
            }

        # 如果没有找到指定策略，尝试使用默认策略
        if strategy != 'standard':
            self.logger.warning(f"⚠️  策略 '{strategy}' 未找到，使用默认策略 'standard'")
            return self._get_strategy_config('standard')

        # 如果连默认策略都没有，返回空配置
        self.logger.warning(f"⚠️  策略 '{strategy}' 未找到，使用基础渲染配置")
        return {}

    def _load_global_strategy_config(self, strategy: str) -> Dict[str, Any]:
        """
        从全局策略配置文件加载策略配置

        Args:
            strategy: 策略名称

        Returns:
            策略配置
        """
        try:
            from pathlib import Path

            # 全局策略配置文件路径
            config_path = Path(__file__).parent.parent / "configs" / "rendering" / "strategies.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    strategies_data = json.load(f)

                # 过滤注释并获取策略配置
                strategies = self._filter_comments(strategies_data).get('strategies', {})
                strategy_config = strategies.get(strategy, {})

                if strategy_config:
                    self.logger.debug(f"✅ 从全局配置加载策略: {strategy}")
                    return strategy_config
                else:
                    self.logger.debug(f"⚠️  全局配置中未找到策略: {strategy}")
                    return {}
            else:
                self.logger.warning(f"⚠️  策略配置文件未找到: {config_path}")
                return {}

        except Exception as e:
            self.logger.warning(f"⚠️  加载全局策略配置失败: {e}")
            return {}

    def retry_request(self, request_func, max_retries: int = None,
                     retry_delay: int = None, *args, **kwargs) -> Optional[Any]:
        """
        带重试的请求执行
        
        Args:
            request_func: 请求函数
            max_retries: 最大重试次数
            retry_delay: 重试延迟
            *args, **kwargs: 请求函数参数
            
        Returns:
            请求结果
        """
        if max_retries is None:
            max_retries = self.config.performance.retry_attempts
        if retry_delay is None:
            retry_delay = self.config.performance.retry_delay
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = request_func(*args, **kwargs)
                if result:  # 成功获取结果
                    if attempt > 0:
                        self.logger.info(f"✅ 重试成功: 第 {attempt + 1} 次尝试")
                    return result
                    
            except Exception as e:
                last_exception = e
                self.logger.warning(f"⚠️  请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries:
                self.logger.info(f"⏱️  等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
        
        self.logger.error(f"❌ 所有重试均失败，最后错误: {last_exception}")
        return None
    
    def extract_data_from_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        从API响应中提取数据
        
        Args:
            response: API响应
            
        Returns:
            提取的数据
        """
        if not response or 'results' not in response:
            self.logger.warning("⚠️  响应中无有效数据")
            return {}
        
        try:
            content = response['results'][0].get('content', {})
            
            # 如果有解析结果，优先使用
            if isinstance(content, dict) and any(key for key in content.keys() if not key.startswith('_')):
                return content
            
            # 否则返回原始HTML内容
            if isinstance(content, str):
                return {"html": content}
            
            return content
            
        except (IndexError, KeyError, TypeError) as e:
            self.logger.error(f"❌ 数据提取失败: {e}")
            return {}
    
    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        验证API响应的有效性
        
        Args:
            response: API响应
            
        Returns:
            是否有效
        """
        if not response:
            return False
        
        if 'results' not in response:
            self.logger.warning("⚠️  响应缺少results字段")
            return False
        
        results = response['results']
        if not results or len(results) == 0:
            self.logger.warning("⚠️  响应results为空")
            return False
        
        first_result = results[0]
        if 'content' not in first_result:
            self.logger.warning("⚠️  响应缺少content字段")
            return False
        
        return True
    
    def build_full_url(self, relative_url: str, base_url: str = None) -> str:
        """
        构建完整URL
        
        Args:
            relative_url: 相对URL
            base_url: 基础URL
            
        Returns:
            完整URL
        """
        if relative_url.startswith('http'):
            return relative_url
        
        if base_url is None:
            base_url = self.module_config.get('walmart', {}).get('base_url', 'https://www.walmart.com')
        
        return f"{base_url.rstrip('/')}/{relative_url.lstrip('/')}"
    
    @abstractmethod
    def process(self, *args, **kwargs) -> Dict[str, Any]:
        """
        抽象处理方法，子类必须实现

        Returns:
            处理结果
        """
        pass


# 便捷函数
def create_base_module(config: WorkflowConfig, module_name: str) -> BaseModule:
    """创建基础模块的便捷函数（仅用于测试）"""
    class TestModule(BaseModule):
        def process(self, *args, **kwargs):
            return {"test": "data"}

    return TestModule(config, module_name)
