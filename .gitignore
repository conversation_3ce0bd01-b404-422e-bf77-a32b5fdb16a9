# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
# 抓取结果文件（忽略生成的数据文件）
walmart_raw_*.json
walmart_categories_*.json
walmart_links_result_*.json
walmart_parser_result_*.json
general_result_*.json
general_analysis_*.json
oxylabs_basic_result_*.json
oxylabs_analysis_*.json
oxylabs_html_analysis_*.json

# 日志文件
*.log

# 临时文件
*.tmp
*.temp
debug.html

# 敏感配置文件（如果包含真实凭据）
config_local.py
credentials.json

# 数据目录（如果重新创建）
data/
logs/
screenshots/
