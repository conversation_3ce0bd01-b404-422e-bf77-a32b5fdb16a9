#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Browser Instructions 简化测试
对比URL参数排序 vs Browser Instructions排序的产品结果
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scraper_apis.oxylabs_api import OxylabsAPI
from utils.logger.logger import get_logger

class SimpleBrowserInstructionsTest:
    """简化的Browser Instructions测试类"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.api = None

        # 测试URL
        self.base_url = "https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_1985043_4929045"
        self.url_sorted = f"{self.base_url}?sort=best_seller&affinityOverride=default"

        # Walmart排序的Browser Instructions（基于实际HTML元素）
        self.browser_instructions = [
            {
                "type": "wait_for_element",
                "selector": {
                    "type": "css",
                    "value": "button[aria-label*='Sort by']"
                },
                "timeout_s": 10
            },
            {
                "type": "click",
                "selector": {
                    "type": "css",
                    "value": "button[aria-label*='Sort by']"
                }
            },
            {
                "type": "wait_for_element",
                "selector": {
                    "type": "css",
                    "value": "input#best_seller"
                },
                "timeout_s": 10
            },
            {
                "type": "click",
                "selector": {
                    "type": "css",
                    "value": "input#best_seller"
                }
            },
            {
                "type": "wait",
                "wait_time_s": 3
            }
        ]

        # 基于实际HTML结构修正的解析指令
        self.parsing_instructions = {
            "products": {
                "_fns": [
                    {
                        "_fn": "css",
                        "_args": ["div[role='group'][data-item-id]"]
                    }
                ],
                "_items": {
                    "title": {
                        "_fns": [
                            {
                                "_fn": "css_one",
                                "_args": ["span.w_iUH7"]
                            },
                            {
                                "_fn": "element_text"
                            }
                        ]
                    },
                    "link": {
                        "_fns": [
                            {
                                "_fn": "css_one",
                                "_args": ["a[link-identifier]"]
                            },
                            {
                                "_fn": "xpath_one",
                                "_args": "./@href"
                            }
                        ]
                    },
                    "link_identifier": {
                        "_fns": [
                            {
                                "_fn": "css_one",
                                "_args": ["a[link-identifier]"]
                            },
                            {
                                "_fn": "xpath_one",
                                "_args": "./@link-identifier"
                            }
                        ]
                    },
                    "data_item_id": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": "./@data-item-id"
                            }
                        ]
                    },
                    "best_seller": {
                        "_fns": [
                            {
                                "_fn": "xpath_one",
                                "_args": ".//span[contains(text(), 'Best seller')]/text()"
                            }
                        ]
                    }
                }
            }
        }
        
    def setup(self):
        """初始化API"""
        try:
            # 加载API配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                     'scraper_apis', 'api_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.api = OxylabsAPI(config['oxylabs'])
            self.logger.info("✅ API初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"❌ API初始化失败: {e}")
            return False
    
    def test_simple_comparison(self):
        """
        简化对比测试：URL参数排序 vs Browser Instructions排序
        """
        self.logger.info("🧪 开始简化对比测试")

        results = {}

        # 测试方法1：URL参数排序（先测试基础请求）
        self.logger.info("📋 测试方法1：URL参数排序（基础请求）")
        try:
            # 先测试基础请求，不使用解析
            response1 = self.api.scrape(
                url=self.url_sorted,
                source="universal_ecommerce",
                render="html"
            )
            
            # 处理Oxylabs原始响应
            if response1 and 'results' in response1 and len(response1['results']) > 0:
                result = response1['results'][0]
                content = result.get('content', '')
                status_code = result.get('status_code', 0)

                results['url_method'] = {
                    'success': True,
                    'has_content': len(content) > 0,
                    'content_length': len(content),
                    'status_code': status_code,
                    'job_id': result.get('job_id', ''),
                    'url': result.get('url', '')
                }
                self.logger.info(f"✅ URL方法成功：状态码 {status_code}，获得 {len(content)} 字符的HTML内容")

                # 简单检查是否包含产品相关内容
                if 'data-item-id' in content:
                    self.logger.info("🎯 HTML中包含产品数据")
                else:
                    self.logger.warning("⚠️  HTML中未找到产品数据")
            else:
                results['url_method'] = {'success': False, 'error': f'API响应异常: {response1}'}
                self.logger.error(f"❌ URL方法失败：{response1}")
                
        except Exception as e:
            results['url_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ URL方法异常：{e}")
        
        # 测试方法2：Browser Instructions排序（基础请求）
        self.logger.info("📋 测试方法2：Browser Instructions排序（基础请求）")
        try:
            response2 = self.api.scrape_with_browser_instructions(
                url=self.base_url,
                browser_instructions=self.browser_instructions
            )
            
            # 处理Oxylabs原始响应
            if response2 and 'results' in response2 and len(response2['results']) > 0:
                result = response2['results'][0]
                content = result.get('content', '')
                status_code = result.get('status_code', 0)

                results['browser_method'] = {
                    'success': True,
                    'has_content': len(content) > 0,
                    'content_length': len(content),
                    'status_code': status_code,
                    'job_id': result.get('job_id', ''),
                    'url': result.get('url', ''),
                    'browser_warnings': result.get('browser_instructions_warnings', []),
                    'browser_errors': result.get('browser_instructions_error', [])
                }
                self.logger.info(f"✅ Browser Instructions成功：状态码 {status_code}，获得 {len(content)} 字符的HTML内容")

                # 检查是否有浏览器指令警告或错误
                if result.get('browser_instructions_warnings'):
                    self.logger.warning(f"⚠️  浏览器指令警告：{result['browser_instructions_warnings']}")
                if result.get('browser_instructions_error'):
                    self.logger.error(f"❌ 浏览器指令错误：{result['browser_instructions_error']}")

                # 简单检查是否包含产品相关内容
                if 'data-item-id' in content:
                    self.logger.info("🎯 HTML中包含产品数据")
                else:
                    self.logger.warning("⚠️  HTML中未找到产品数据")

            else:
                results['browser_method'] = {'success': False, 'error': f'API响应异常: {response2}'}
                self.logger.error(f"❌ Browser Instructions失败：{response2}")
                
        except Exception as e:
            results['browser_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ Browser Instructions异常：{e}")
        
        # 保存测试结果
        self._save_test_results(results)

        # 分析结果
        self._analyze_results(results)

        return results

    def test_with_parsing(self):
        """
        使用解析器对比产品结果
        """
        self.logger.info("🧪 开始解析对比测试")

        results = {}

        # 测试方法1：URL参数排序 + 解析
        self.logger.info("📋 测试方法1：URL参数排序 + 解析")
        try:
            response1 = self.api.scrape_with_parsing(
                url=self.url_sorted,
                parsing_instructions=self.parsing_instructions
            )

            # 处理Oxylabs原始响应
            if response1 and 'results' in response1 and len(response1['results']) > 0:
                result = response1['results'][0]
                parsed_data = result.get('results', {})
                products = parsed_data.get('products', [])

                results['url_method'] = {
                    'success': True,
                    'product_count': len(products),
                    'products': products[:3],  # 保存前3个产品用于对比
                    'status_code': result.get('status_code', 0)
                }
                self.logger.info(f"✅ URL方法解析成功：{len(products)} 个产品")

                # 显示前几个产品
                for i, product in enumerate(products[:3]):
                    title = product.get('title') or product.get('title_fallback', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                results['url_method'] = {'success': False, 'error': f'解析失败: {response1}'}
                self.logger.error(f"❌ URL方法解析失败：{response1}")

        except Exception as e:
            results['url_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ URL方法异常：{e}")

        # 测试方法2：Browser Instructions + 解析
        self.logger.info("📋 测试方法2：Browser Instructions + 解析")
        try:
            response2 = self.api.scrape_with_browser_instructions(
                url=self.base_url,
                browser_instructions=self.browser_instructions,
                parsing_instructions=self.parsing_instructions
            )

            # 处理Oxylabs原始响应
            if response2 and 'results' in response2 and len(response2['results']) > 0:
                result = response2['results'][0]
                parsed_data = result.get('results', {})
                products = parsed_data.get('products', [])

                results['browser_method'] = {
                    'success': True,
                    'product_count': len(products),
                    'products': products[:3],  # 保存前3个产品用于对比
                    'status_code': result.get('status_code', 0),
                    'browser_warnings': result.get('browser_instructions_warnings', [])
                }
                self.logger.info(f"✅ Browser Instructions解析成功：{len(products)} 个产品")

                # 检查浏览器指令警告
                if result.get('browser_instructions_warnings'):
                    self.logger.warning(f"⚠️  浏览器指令警告：{result['browser_instructions_warnings']}")

                # 显示前几个产品
                for i, product in enumerate(products[:3]):
                    title = product.get('title') or product.get('title_fallback', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                results['browser_method'] = {'success': False, 'error': f'解析失败: {response2}'}
                self.logger.error(f"❌ Browser Instructions解析失败：{response2}")

        except Exception as e:
            results['browser_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ Browser Instructions异常：{e}")

        # 保存测试结果
        self._save_test_results(results, "parsing")

        # 分析解析结果
        self._analyze_parsing_results(results)

        return results
    
    def _save_test_results(self, results, test_type="basic"):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"browser_instructions_{test_type}_test_{timestamp}.json"
        filepath = os.path.join("tests", filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"📄 测试结果已保存：{filepath}")
        except Exception as e:
            self.logger.error(f"❌ 保存测试结果失败：{e}")

    def _analyze_parsing_results(self, results):
        """分析解析测试结果"""
        self.logger.info("📊 解析结果分析")
        self.logger.info("=" * 50)

        url_success = results.get('url_method', {}).get('success', False)
        browser_success = results.get('browser_method', {}).get('success', False)

        if url_success and browser_success:
            url_count = results['url_method']['product_count']
            browser_count = results['browser_method']['product_count']

            self.logger.info(f"✅ 两种方法都成功解析")
            self.logger.info(f"📦 URL方法：{url_count} 个产品")
            self.logger.info(f"🌐 Browser方法：{browser_count} 个产品")

            # 对比产品数量
            if url_count == browser_count:
                self.logger.info("🎯 产品数量一致")
            else:
                diff = abs(url_count - browser_count)
                self.logger.warning(f"⚠️  产品数量差异：{diff} 个产品")

            # 对比前几个产品
            url_products = results['url_method'].get('products', [])
            browser_products = results['browser_method'].get('products', [])

            self.logger.info("🔍 产品对比分析：")
            for i in range(min(3, len(url_products), len(browser_products))):
                url_title = url_products[i].get('title') or url_products[i].get('title_fallback', 'N/A')
                browser_title = browser_products[i].get('title') or browser_products[i].get('title_fallback', 'N/A')

                if url_title == browser_title:
                    self.logger.info(f"  产品{i+1}: ✅ 标题一致")
                else:
                    self.logger.warning(f"  产品{i+1}: ⚠️  标题不同")
                    self.logger.info(f"    URL方法: {url_title[:50]}...")
                    self.logger.info(f"    Browser方法: {browser_title[:50]}...")

            # 检查Best Seller标记
            url_best_sellers = sum(1 for p in url_products if p.get('best_seller'))
            browser_best_sellers = sum(1 for p in browser_products if p.get('best_seller'))

            self.logger.info(f"🏆 Best Seller产品数量：")
            self.logger.info(f"  URL方法: {url_best_sellers} 个")
            self.logger.info(f"  Browser方法: {browser_best_sellers} 个")

            # 分析Browser Instructions的效果
            browser_warnings = results['browser_method'].get('browser_warnings', [])
            if browser_warnings:
                self.logger.warning(f"🔧 Browser Instructions有警告，排序可能未生效")
            else:
                self.logger.info(f"✅ Browser Instructions执行成功，排序已生效")

        elif url_success and not browser_success:
            self.logger.warning("⚠️  URL方法成功，Browser Instructions失败")

        elif not url_success and browser_success:
            self.logger.info("🎉 Browser Instructions成功，URL方法失败")

        else:
            self.logger.error("❌ 两种方法都失败")
    
    def _analyze_results(self, results):
        """分析测试结果"""
        self.logger.info("📊 测试结果分析")
        self.logger.info("=" * 50)
        
        url_success = results.get('url_method', {}).get('success', False)
        browser_success = results.get('browser_method', {}).get('success', False)
        
        if url_success and browser_success:
            url_length = results['url_method']['content_length']
            browser_length = results['browser_method']['content_length']
            url_status = results['url_method']['status_code']
            browser_status = results['browser_method']['status_code']

            self.logger.info(f"✅ 两种方法都成功")
            self.logger.info(f"📦 URL方法：{url_length} 字符HTML，状态码 {url_status}")
            self.logger.info(f"🌐 Browser方法：{browser_length} 字符HTML，状态码 {browser_status}")

            length_diff = abs(url_length - browser_length)
            length_diff_percent = (length_diff / max(url_length, browser_length)) * 100

            self.logger.info(f"📊 HTML长度差异：{length_diff} 字符 ({length_diff_percent:.1f}%)")

            if length_diff_percent < 10:
                self.logger.info("🎯 HTML长度相近，两种方法效果相似")
            else:
                self.logger.warning(f"⚠️  HTML长度差异较大，可能存在显著差异")

            # 分析Browser Instructions的效果
            browser_warnings = results['browser_method'].get('browser_warnings', [])
            if browser_warnings:
                self.logger.warning(f"🔧 Browser Instructions有警告，可能需要调整选择器")
            
        elif url_success and not browser_success:
            self.logger.warning("⚠️  URL方法成功，Browser Instructions失败")
            self.logger.info("💡 建议检查Browser Instructions的选择器和逻辑")
            
        elif not url_success and browser_success:
            self.logger.info("🎉 Browser Instructions成功，URL方法失败")
            self.logger.info("💪 Browser Instructions更健壮！")
            
        else:
            self.logger.error("❌ 两种方法都失败")
            self.logger.info("🔧 需要检查网站结构或API配置")

def main():
    """主函数"""
    print("🧪 Browser Instructions 完整测试")
    print("=" * 50)

    test = SimpleBrowserInstructionsTest()

    if not test.setup():
        print("❌ 测试初始化失败")
        return

    # 执行基础测试
    print("\n🔍 第一阶段：基础请求测试")
    basic_results = test.test_simple_comparison()

    # 执行解析测试
    print("\n🔍 第二阶段：解析对比测试")
    parsing_results = test.test_with_parsing()

    print("\n🎯 所有测试完成！")
    print("📄 详细结果请查看生成的JSON文件")

if __name__ == "__main__":
    main()
