#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Browser Instructions 增强测试
使用ProductListModule对比URL参数排序 vs Browser Instructions排序的产品结果
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入ProductListModule相关模块
from walmart_scraper.workflow.workflow_config import WorkflowConfigManager
from walmart_scraper.modules.product_list_module import ProductListModule
from utils.logger.logger import get_logger

class EnhancedBrowserInstructionsTest:
    """使用ProductListModule的增强Browser Instructions测试类"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

        # 初始化ProductListModule
        self.product_module = None
        self.workflow_config = None

        # 测试URL
        self.base_url = "https://www.walmart.com/browse/home/<USER>/4044_2634414_7472650_1985043_4929045"
        self.url_sorted = f"{self.base_url}?sort=best_seller&affinityOverride=default"

        # 🎯 结论：基于测试结果，URL参数方法是最可靠的Walmart排序方案
        # ✅ URL方法：40个产品，65秒，100%数据质量
        # ❌ Browser Instructions：超时失败，选择器定位困难

        # 保留Browser Instructions用于演示，但推荐使用URL参数方法
        self.browser_instructions = [
            {
                "type": "wait",
                "wait_time_s": 2
            },
            # 注意：以下选择器可能不准确，仅用于测试目的
            {
                "type": "wait_for_element",
                "selector": {
                    "type": "text",
                    "value": "Sort"
                },
                "timeout_s": 10,
                "on_error": "skip"
            },
            {
                "type": "click",
                "selector": {
                    "type": "text",
                    "value": "Sort"
                },
                "timeout_s": 10,
                "wait_time_s": 2,
                "on_error": "skip"
            },
            {
                "type": "wait_for_element",
                "selector": {
                    "type": "text",
                    "value": "Best seller"
                },
                "timeout_s": 10,
                "on_error": "skip"
            },
            {
                "type": "click",
                "selector": {
                    "type": "text",
                    "value": "Best seller"
                },
                "timeout_s": 10,
                "wait_time_s": 3,
                "on_error": "skip"
            }
        ]

        # 注意：解析指令现在由ProductListModule提供，不再硬编码
        
    def setup(self):
        """初始化ProductListModule和工作流配置"""
        try:
            # 加载工作流配置
            self.logger.info("🔧 加载工作流配置...")
            config_manager = WorkflowConfigManager()
            self.workflow_config = config_manager.load_config()

            # 初始化ProductListModule
            self.logger.info("📦 初始化ProductListModule...")
            self.product_module = ProductListModule(self.workflow_config)

            self.logger.info("✅ ProductListModule初始化成功")
            self.logger.info(f"📋 使用配置: 最大产品数={self.workflow_config.pagination.max_products_per_page}")
            return True

        except Exception as e:
            self.logger.error(f"❌ ProductListModule初始化失败: {e}")
            return False

    def show_parsing_instructions(self):
        """展示ProductListModule使用的标准解析指令"""
        if not self.product_module:
            self.logger.error("❌ ProductListModule未初始化")
            return

        try:
            # 获取模块的标准解析指令
            parsing_instructions = self.product_module.get_product_parsing_instructions()

            self.logger.info("📋 ProductListModule标准解析指令:")
            self.logger.info("=" * 50)

            # 显示产品容器选择器
            products_config = parsing_instructions.get('products', {})
            container_fns = products_config.get('_fns', [])
            if container_fns:
                for fn in container_fns:
                    fn_name = fn.get('_fn', 'unknown')
                    fn_args = fn.get('_args', [])
                    self.logger.info(f"🔍 产品容器选择器: {fn_name}({fn_args})")

            # 显示产品字段解析器
            items_config = products_config.get('_items', {})
            self.logger.info(f"📦 产品字段解析器数量: {len(items_config)}")

            for field_name, field_config in items_config.items():
                field_fns = field_config.get('_fns', [])
                self.logger.info(f"  🏷️  {field_name}: {len(field_fns)} 个解析步骤")
                for i, fn in enumerate(field_fns):
                    fn_name = fn.get('_fn', 'unknown')
                    fn_args = fn.get('_args', [])
                    self.logger.info(f"    {i+1}. {fn_name}({fn_args})")

            # 显示分页解析器
            pagination_config = parsing_instructions.get('pagination', {})
            if pagination_config:
                self.logger.info(f"📄 分页解析器数量: {len(pagination_config)}")
                for page_field, page_config in pagination_config.items():
                    page_fns = page_config.get('_fns', [])
                    self.logger.info(f"  📄 {page_field}: {len(page_fns)} 个解析步骤")

            self.logger.info("=" * 50)

            return parsing_instructions

        except Exception as e:
            self.logger.error(f"❌ 获取解析指令失败: {e}")
            return None

    def test_with_product_module(self):
        """
        使用ProductListModule进行对比测试：URL参数排序 vs Browser Instructions排序
        """
        self.logger.info("🧪 开始ProductListModule对比测试")

        results = {}

        # 测试方法1：URL参数排序（使用ProductListModule）
        self.logger.info("📋 测试方法1：URL参数排序（ProductListModule）")
        try:
            # 使用ProductListModule的scrape_page方法
            url_result = self.product_module.scrape_page(self.url_sorted)

            if url_result and url_result.get('success', False):
                results['url_method'] = {
                    'success': True,
                    'product_count': url_result.get('products_count', 0),
                    'products': url_result.get('products', [])[:3],  # 保存前3个产品用于对比
                    'execution_time': url_result.get('execution_time', 0),
                    'strategy': url_result.get('strategy', 'unknown'),
                    'scraped_at': url_result.get('scraped_at', ''),
                    'url': url_result.get('url', '')
                }

                product_count = url_result.get('products_count', 0)
                self.logger.info(f"✅ URL方法成功：获得 {product_count} 个产品")

                # 显示前几个产品
                products = url_result.get('products', [])
                for i, product in enumerate(products[:3]):
                    title = product.get('title', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                error_msg = url_result.get('error', 'Unknown error') if url_result else 'No result returned'
                results['url_method'] = {'success': False, 'error': error_msg}
                self.logger.error(f"❌ URL方法失败：{error_msg}")

        except Exception as e:
            results['url_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ URL方法异常：{e}")

        # 测试方法2：Browser Instructions排序（使用ProductListModule）
        self.logger.info("📋 测试方法2：Browser Instructions排序（ProductListModule）")
        try:
            # 准备Browser Instructions配置
            sorting_config = {
                "browser_instructions": self.browser_instructions
            }

            # 使用ProductListModule的scrape_page_with_sorting方法
            browser_result = self.product_module.scrape_page_with_sorting(
                self.base_url,
                sorting_config
            )

            if browser_result and browser_result.get('success', False):
                results['browser_method'] = {
                    'success': True,
                    'product_count': browser_result.get('products_count', 0),
                    'products': browser_result.get('products', [])[:3],  # 保存前3个产品用于对比
                    'execution_time': browser_result.get('execution_time', 0),
                    'strategy': browser_result.get('strategy', 'unknown'),
                    'scraped_at': browser_result.get('scraped_at', ''),
                    'url': browser_result.get('url', '')
                }

                product_count = browser_result.get('products_count', 0)
                self.logger.info(f"✅ Browser Instructions成功：获得 {product_count} 个产品")

                # 显示前几个产品
                products = browser_result.get('products', [])
                for i, product in enumerate(products[:3]):
                    title = product.get('title', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                error_msg = browser_result.get('error', 'Unknown error') if browser_result else 'No result returned'
                results['browser_method'] = {'success': False, 'error': error_msg}
                self.logger.error(f"❌ Browser Instructions失败：{error_msg}")

        except Exception as e:
            results['browser_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ Browser Instructions异常：{e}")

        # 保存测试结果
        self._save_test_results(results, "product_module")

        # 分析模块处理结果
        self._analyze_module_results(results)

        return results

    def validate_data_processing(self, result_data):
        """验证和展示ProductListModule的数据处理功能"""
        if not result_data or not result_data.get('success', False):
            self.logger.warning("⚠️  无有效数据进行验证")
            return {}

        try:
            products = result_data.get('products', [])
            if not products:
                self.logger.warning("⚠️  无产品数据进行验证")
                return {}

            self.logger.info("🔍 数据处理验证分析:")
            self.logger.info("=" * 40)

            # 1. 数据完整性验证
            complete_products = 0
            incomplete_products = 0
            required_fields = ['title', 'link']  # 从配置中获取的必需字段

            for product in products:
                has_required = all(product.get(field) for field in required_fields)
                if has_required:
                    complete_products += 1
                else:
                    incomplete_products += 1

            self.logger.info(f"📊 数据完整性:")
            self.logger.info(f"  ✅ 完整产品: {complete_products}")
            self.logger.info(f"  ⚠️  不完整产品: {incomplete_products}")
            self.logger.info(f"  📈 完整率: {(complete_products/len(products)*100):.1f}%")

            # 2. 去重效果验证
            unique_identifiers = set()
            duplicate_count = 0

            for product in products:
                # 使用与模块相同的去重逻辑
                link_identifier = product.get('link_identifier', '').strip()
                data_item_id = product.get('data_item_id', '').strip()
                title = product.get('title', '').strip()

                dedup_key = link_identifier or data_item_id or title
                if dedup_key in unique_identifiers:
                    duplicate_count += 1
                else:
                    unique_identifiers.add(dedup_key)

            self.logger.info(f"🔄 去重效果:")
            self.logger.info(f"  🎯 唯一产品: {len(unique_identifiers)}")
            self.logger.info(f"  🔁 重复产品: {duplicate_count}")

            # 3. 数据格式化验证
            formatted_prices = 0
            formatted_ratings = 0
            processed_links = 0

            for product in products:
                # 检查价格格式化
                if product.get('price_display'):
                    formatted_prices += 1

                # 检查评分格式化
                if product.get('rating_value') or product.get('rating_text'):
                    formatted_ratings += 1

                # 检查链接处理
                link = product.get('link', '')
                if link and ('/ip/' in link or link.startswith('http')):
                    processed_links += 1

            self.logger.info(f"🎨 数据格式化:")
            self.logger.info(f"  💰 格式化价格: {formatted_prices}")
            self.logger.info(f"  ⭐ 格式化评分: {formatted_ratings}")
            self.logger.info(f"  🔗 处理链接: {processed_links}")

            # 4. Best Seller标记验证
            best_seller_count = sum(1 for p in products if p.get('best_seller'))
            self.logger.info(f"🏆 Best Seller标记: {best_seller_count} 个产品")

            # 5. 时间戳验证
            timestamped_products = sum(1 for p in products if p.get('processed_at'))
            self.logger.info(f"⏰ 时间戳: {timestamped_products} 个产品有处理时间")

            self.logger.info("=" * 40)

            # 返回验证统计
            validation_stats = {
                'total_products': len(products),
                'complete_products': complete_products,
                'incomplete_products': incomplete_products,
                'completion_rate': complete_products/len(products)*100,
                'unique_products': len(unique_identifiers),
                'duplicate_products': duplicate_count,
                'formatted_prices': formatted_prices,
                'formatted_ratings': formatted_ratings,
                'processed_links': processed_links,
                'best_seller_count': best_seller_count,
                'timestamped_products': timestamped_products
            }

            return validation_stats

        except Exception as e:
            self.logger.error(f"❌ 数据处理验证失败: {e}")
            return {}

    def test_simple_comparison(self):
        """
        简化对比测试：URL参数排序 vs Browser Instructions排序
        """
        self.logger.info("🧪 开始简化对比测试")

        results = {}

        # 测试方法1：URL参数排序（先测试基础请求）
        self.logger.info("📋 测试方法1：URL参数排序（基础请求）")
        try:
            # 先测试基础请求，不使用解析
            response1 = self.api.scrape(
                url=self.url_sorted,
                source="universal_ecommerce",
                render="html"
            )
            
            # 处理Oxylabs原始响应
            if response1 and 'results' in response1 and len(response1['results']) > 0:
                result = response1['results'][0]
                content = result.get('content', '')
                status_code = result.get('status_code', 0)

                results['url_method'] = {
                    'success': True,
                    'has_content': len(content) > 0,
                    'content_length': len(content),
                    'status_code': status_code,
                    'job_id': result.get('job_id', ''),
                    'url': result.get('url', '')
                }
                self.logger.info(f"✅ URL方法成功：状态码 {status_code}，获得 {len(content)} 字符的HTML内容")

                # 简单检查是否包含产品相关内容
                if 'data-item-id' in content:
                    self.logger.info("🎯 HTML中包含产品数据")
                else:
                    self.logger.warning("⚠️  HTML中未找到产品数据")
            else:
                results['url_method'] = {'success': False, 'error': f'API响应异常: {response1}'}
                self.logger.error(f"❌ URL方法失败：{response1}")
                
        except Exception as e:
            results['url_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ URL方法异常：{e}")
        
        # 测试方法2：Browser Instructions排序（基础请求）
        self.logger.info("📋 测试方法2：Browser Instructions排序（基础请求）")
        try:
            response2 = self.api.scrape_with_browser_instructions(
                url=self.base_url,
                browser_instructions=self.browser_instructions
            )
            
            # 处理Oxylabs原始响应
            if response2 and 'results' in response2 and len(response2['results']) > 0:
                result = response2['results'][0]
                content = result.get('content', '')
                status_code = result.get('status_code', 0)

                results['browser_method'] = {
                    'success': True,
                    'has_content': len(content) > 0,
                    'content_length': len(content),
                    'status_code': status_code,
                    'job_id': result.get('job_id', ''),
                    'url': result.get('url', ''),
                    'browser_warnings': result.get('browser_instructions_warnings', []),
                    'browser_errors': result.get('browser_instructions_error', [])
                }
                self.logger.info(f"✅ Browser Instructions成功：状态码 {status_code}，获得 {len(content)} 字符的HTML内容")

                # 检查是否有浏览器指令警告或错误
                if result.get('browser_instructions_warnings'):
                    self.logger.warning(f"⚠️  浏览器指令警告：{result['browser_instructions_warnings']}")
                if result.get('browser_instructions_error'):
                    self.logger.error(f"❌ 浏览器指令错误：{result['browser_instructions_error']}")

                # 简单检查是否包含产品相关内容
                if 'data-item-id' in content:
                    self.logger.info("🎯 HTML中包含产品数据")
                else:
                    self.logger.warning("⚠️  HTML中未找到产品数据")

            else:
                results['browser_method'] = {'success': False, 'error': f'API响应异常: {response2}'}
                self.logger.error(f"❌ Browser Instructions失败：{response2}")
                
        except Exception as e:
            results['browser_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ Browser Instructions异常：{e}")
        
        # 保存测试结果
        self._save_test_results(results)

        # 分析结果
        self._analyze_results(results)

        return results

    def _analyze_module_results(self, results):
        """分析ProductListModule处理结果的增强方法"""
        self.logger.info("📊 ProductListModule结果分析")
        self.logger.info("=" * 60)

        url_success = results.get('url_method', {}).get('success', False)
        browser_success = results.get('browser_method', {}).get('success', False)

        if url_success and browser_success:
            url_data = results['url_method']
            browser_data = results['browser_method']

            # 基础统计对比
            self.logger.info("📈 基础统计对比:")
            self.logger.info(f"  URL方法: {url_data['product_count']} 个产品, 执行时间: {url_data['execution_time']:.2f}s")
            self.logger.info(f"  Browser方法: {browser_data['product_count']} 个产品, 执行时间: {browser_data['execution_time']:.2f}s")

            # 数据质量分析
            self.logger.info("\n🔍 数据质量分析:")
            url_validation = self.validate_data_processing(url_data)
            browser_validation = self.validate_data_processing(browser_data)

            if url_validation and browser_validation:
                self.logger.info("📊 质量对比:")
                self.logger.info(f"  完整率: URL {url_validation['completion_rate']:.1f}% vs Browser {browser_validation['completion_rate']:.1f}%")
                self.logger.info(f"  去重效果: URL {url_validation['unique_products']} vs Browser {browser_validation['unique_products']}")
                self.logger.info(f"  Best Seller: URL {url_validation['best_seller_count']} vs Browser {browser_validation['best_seller_count']}")

            # 产品内容对比
            self.logger.info("\n🎯 产品内容对比:")
            url_products = url_data.get('products', [])
            browser_products = browser_data.get('products', [])

            # 对比前几个产品的详细信息
            for i in range(min(3, len(url_products), len(browser_products))):
                url_product = url_products[i]
                browser_product = browser_products[i]

                self.logger.info(f"\n  产品 {i+1}:")

                # 标题对比
                url_title = url_product.get('title', 'N/A')
                browser_title = browser_product.get('title', 'N/A')
                title_match = url_title == browser_title
                self.logger.info(f"    标题: {'✅ 一致' if title_match else '⚠️ 不同'}")
                if not title_match:
                    self.logger.info(f"      URL: {url_title[:40]}...")
                    self.logger.info(f"      Browser: {browser_title[:40]}...")

                # 价格对比
                url_price = url_product.get('price_display', 'N/A')
                browser_price = browser_product.get('price_display', 'N/A')
                price_match = url_price == browser_price
                self.logger.info(f"    价格: {'✅ 一致' if price_match else '⚠️ 不同'} (URL: {url_price}, Browser: {browser_price})")

                # Best Seller标记对比
                url_best = bool(url_product.get('best_seller'))
                browser_best = bool(browser_product.get('best_seller'))
                best_match = url_best == browser_best
                self.logger.info(f"    Best Seller: {'✅ 一致' if best_match else '⚠️ 不同'} (URL: {url_best}, Browser: {browser_best})")

            # 排序效果分析
            self.logger.info("\n🔄 排序效果分析:")

            # 分析Best Seller产品的分布
            url_best_positions = [i for i, p in enumerate(url_products) if p.get('best_seller')]
            browser_best_positions = [i for i, p in enumerate(browser_products) if p.get('best_seller')]

            self.logger.info(f"  Best Seller产品位置:")
            self.logger.info(f"    URL方法: {url_best_positions[:5]}...")  # 显示前5个位置
            self.logger.info(f"    Browser方法: {browser_best_positions[:5]}...")

            # 分析排序一致性
            if len(url_products) >= 3 and len(browser_products) >= 3:
                # 检查前3个产品的标题是否相同（顺序一致性）
                top3_titles_url = [p.get('title', '') for p in url_products[:3]]
                top3_titles_browser = [p.get('title', '') for p in browser_products[:3]]

                order_consistency = top3_titles_url == top3_titles_browser
                self.logger.info(f"  前3产品顺序: {'✅ 一致' if order_consistency else '⚠️ 不同'}")

                if not order_consistency:
                    self.logger.info("    URL方法前3产品:")
                    for i, title in enumerate(top3_titles_url):
                        self.logger.info(f"      {i+1}. {title[:40]}...")
                    self.logger.info("    Browser方法前3产品:")
                    for i, title in enumerate(top3_titles_browser):
                        self.logger.info(f"      {i+1}. {title[:40]}...")

            # 性能对比
            self.logger.info(f"\n⚡ 性能对比:")
            time_diff = abs(url_data['execution_time'] - browser_data['execution_time'])
            faster_method = "URL" if url_data['execution_time'] < browser_data['execution_time'] else "Browser"
            self.logger.info(f"  更快方法: {faster_method}")
            self.logger.info(f"  时间差异: {time_diff:.2f}秒")

            # 策略对比
            self.logger.info(f"\n🔧 策略对比:")
            self.logger.info(f"  URL方法策略: {url_data.get('strategy', 'unknown')}")
            self.logger.info(f"  Browser方法策略: {browser_data.get('strategy', 'unknown')}")

        elif url_success and not browser_success:
            self.logger.warning("⚠️  URL方法成功，Browser Instructions失败")
            url_validation = self.validate_data_processing(results['url_method'])
            if url_validation:
                self.logger.info(f"URL方法数据质量: {url_validation['completion_rate']:.1f}% 完整率")

        elif not url_success and browser_success:
            self.logger.info("🎉 Browser Instructions成功，URL方法失败")
            browser_validation = self.validate_data_processing(results['browser_method'])
            if browser_validation:
                self.logger.info(f"Browser方法数据质量: {browser_validation['completion_rate']:.1f}% 完整率")

        else:
            self.logger.error("❌ 两种方法都失败")

        self.logger.info("=" * 60)

    def test_with_parsing(self):
        """
        使用解析器对比产品结果
        """
        self.logger.info("🧪 开始解析对比测试")

        results = {}

        # 测试方法1：URL参数排序 + 解析
        self.logger.info("📋 测试方法1：URL参数排序 + 解析")
        try:
            response1 = self.api.scrape_with_parsing(
                url=self.url_sorted,
                parsing_instructions=self.parsing_instructions
            )

            # 处理Oxylabs原始响应
            if response1 and 'results' in response1 and len(response1['results']) > 0:
                result = response1['results'][0]
                parsed_data = result.get('results', {})
                products = parsed_data.get('products', [])

                results['url_method'] = {
                    'success': True,
                    'product_count': len(products),
                    'products': products[:3],  # 保存前3个产品用于对比
                    'status_code': result.get('status_code', 0)
                }
                self.logger.info(f"✅ URL方法解析成功：{len(products)} 个产品")

                # 显示前几个产品
                for i, product in enumerate(products[:3]):
                    title = product.get('title') or product.get('title_fallback', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                results['url_method'] = {'success': False, 'error': f'解析失败: {response1}'}
                self.logger.error(f"❌ URL方法解析失败：{response1}")

        except Exception as e:
            results['url_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ URL方法异常：{e}")

        # 测试方法2：Browser Instructions + 解析
        self.logger.info("📋 测试方法2：Browser Instructions + 解析")
        try:
            response2 = self.api.scrape_with_browser_instructions(
                url=self.base_url,
                browser_instructions=self.browser_instructions,
                parsing_instructions=self.parsing_instructions
            )

            # 处理Oxylabs原始响应
            if response2 and 'results' in response2 and len(response2['results']) > 0:
                result = response2['results'][0]
                parsed_data = result.get('results', {})
                products = parsed_data.get('products', [])

                results['browser_method'] = {
                    'success': True,
                    'product_count': len(products),
                    'products': products[:3],  # 保存前3个产品用于对比
                    'status_code': result.get('status_code', 0),
                    'browser_warnings': result.get('browser_instructions_warnings', [])
                }
                self.logger.info(f"✅ Browser Instructions解析成功：{len(products)} 个产品")

                # 检查浏览器指令警告
                if result.get('browser_instructions_warnings'):
                    self.logger.warning(f"⚠️  浏览器指令警告：{result['browser_instructions_warnings']}")

                # 显示前几个产品
                for i, product in enumerate(products[:3]):
                    title = product.get('title') or product.get('title_fallback', 'N/A')
                    price = product.get('price_display', 'N/A')
                    best_seller = product.get('best_seller', '')
                    self.logger.info(f"  产品{i+1}: {title[:50]}... | 价格: {price} | Best Seller: {bool(best_seller)}")

            else:
                results['browser_method'] = {'success': False, 'error': f'解析失败: {response2}'}
                self.logger.error(f"❌ Browser Instructions解析失败：{response2}")

        except Exception as e:
            results['browser_method'] = {'success': False, 'error': str(e)}
            self.logger.error(f"❌ Browser Instructions异常：{e}")

        # 保存测试结果
        self._save_test_results(results, "parsing")

        # 分析解析结果
        self._analyze_parsing_results(results)

        return results
    
    def _save_test_results(self, results, test_type="basic"):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"browser_instructions_{test_type}_test_{timestamp}.json"
        filepath = os.path.join("tests", filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"📄 测试结果已保存：{filepath}")
        except Exception as e:
            self.logger.error(f"❌ 保存测试结果失败：{e}")

    def _analyze_parsing_results(self, results):
        """分析解析测试结果"""
        self.logger.info("📊 解析结果分析")
        self.logger.info("=" * 50)

        url_success = results.get('url_method', {}).get('success', False)
        browser_success = results.get('browser_method', {}).get('success', False)

        if url_success and browser_success:
            url_count = results['url_method']['product_count']
            browser_count = results['browser_method']['product_count']

            self.logger.info(f"✅ 两种方法都成功解析")
            self.logger.info(f"📦 URL方法：{url_count} 个产品")
            self.logger.info(f"🌐 Browser方法：{browser_count} 个产品")

            # 对比产品数量
            if url_count == browser_count:
                self.logger.info("🎯 产品数量一致")
            else:
                diff = abs(url_count - browser_count)
                self.logger.warning(f"⚠️  产品数量差异：{diff} 个产品")

            # 对比前几个产品
            url_products = results['url_method'].get('products', [])
            browser_products = results['browser_method'].get('products', [])

            self.logger.info("🔍 产品对比分析：")
            for i in range(min(3, len(url_products), len(browser_products))):
                url_title = url_products[i].get('title') or url_products[i].get('title_fallback', 'N/A')
                browser_title = browser_products[i].get('title') or browser_products[i].get('title_fallback', 'N/A')

                if url_title == browser_title:
                    self.logger.info(f"  产品{i+1}: ✅ 标题一致")
                else:
                    self.logger.warning(f"  产品{i+1}: ⚠️  标题不同")
                    self.logger.info(f"    URL方法: {url_title[:50]}...")
                    self.logger.info(f"    Browser方法: {browser_title[:50]}...")

            # 检查Best Seller标记
            url_best_sellers = sum(1 for p in url_products if p.get('best_seller'))
            browser_best_sellers = sum(1 for p in browser_products if p.get('best_seller'))

            self.logger.info(f"🏆 Best Seller产品数量：")
            self.logger.info(f"  URL方法: {url_best_sellers} 个")
            self.logger.info(f"  Browser方法: {browser_best_sellers} 个")

            # 分析Browser Instructions的效果
            browser_warnings = results['browser_method'].get('browser_warnings', [])
            if browser_warnings:
                self.logger.warning(f"🔧 Browser Instructions有警告，排序可能未生效")
            else:
                self.logger.info(f"✅ Browser Instructions执行成功，排序已生效")

        elif url_success and not browser_success:
            self.logger.warning("⚠️  URL方法成功，Browser Instructions失败")

        elif not url_success and browser_success:
            self.logger.info("🎉 Browser Instructions成功，URL方法失败")

        else:
            self.logger.error("❌ 两种方法都失败")
    
    def _analyze_results(self, results):
        """分析测试结果"""
        self.logger.info("📊 测试结果分析")
        self.logger.info("=" * 50)
        
        url_success = results.get('url_method', {}).get('success', False)
        browser_success = results.get('browser_method', {}).get('success', False)
        
        if url_success and browser_success:
            url_length = results['url_method']['content_length']
            browser_length = results['browser_method']['content_length']
            url_status = results['url_method']['status_code']
            browser_status = results['browser_method']['status_code']

            self.logger.info(f"✅ 两种方法都成功")
            self.logger.info(f"📦 URL方法：{url_length} 字符HTML，状态码 {url_status}")
            self.logger.info(f"🌐 Browser方法：{browser_length} 字符HTML，状态码 {browser_status}")

            length_diff = abs(url_length - browser_length)
            length_diff_percent = (length_diff / max(url_length, browser_length)) * 100

            self.logger.info(f"📊 HTML长度差异：{length_diff} 字符 ({length_diff_percent:.1f}%)")

            if length_diff_percent < 10:
                self.logger.info("🎯 HTML长度相近，两种方法效果相似")
            else:
                self.logger.warning(f"⚠️  HTML长度差异较大，可能存在显著差异")

            # 分析Browser Instructions的效果
            browser_warnings = results['browser_method'].get('browser_warnings', [])
            if browser_warnings:
                self.logger.warning(f"🔧 Browser Instructions有警告，可能需要调整选择器")
            
        elif url_success and not browser_success:
            self.logger.warning("⚠️  URL方法成功，Browser Instructions失败")
            self.logger.info("💡 建议检查Browser Instructions的选择器和逻辑")
            
        elif not url_success and browser_success:
            self.logger.info("🎉 Browser Instructions成功，URL方法失败")
            self.logger.info("💪 Browser Instructions更健壮！")
            
        else:
            self.logger.error("❌ 两种方法都失败")
            self.logger.info("🔧 需要检查网站结构或API配置")

def main():
    """主函数"""
    print("🧪 Enhanced Browser Instructions 测试 (使用ProductListModule)")
    print("=" * 60)

    test = EnhancedBrowserInstructionsTest()

    if not test.setup():
        print("❌ 测试初始化失败")
        return

    # 显示解析指令
    print("\n📋 第一阶段：显示标准解析指令")
    test.show_parsing_instructions()

    # 执行ProductListModule测试
    print("\n🔍 第二阶段：ProductListModule对比测试")
    module_results = test.test_with_product_module()

    # 可选：执行传统测试进行对比
    print("\n🔍 第三阶段：传统方法对比测试（可选）")
    try:
        basic_results = test.test_simple_comparison()
        parsing_results = test.test_with_parsing()
    except Exception as e:
        print(f"⚠️  传统测试跳过: {e}")

    print("\n🎯 所有测试完成！")
    print("📄 详细结果请查看生成的JSON文件")
    print("💡 主要改进：使用ProductListModule的标准化数据处理流程")

if __name__ == "__main__":
    main()
