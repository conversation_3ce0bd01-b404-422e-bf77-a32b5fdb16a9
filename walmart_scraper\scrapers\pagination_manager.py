#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页管理器
智能处理分页逻辑，自动检测最大页数，实现页面限制控制
"""

import re
import time
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

from .base_scraper import BaseScraper
from ..workflow.workflow_config import WorkflowConfig


class PaginationManager(BaseScraper):
    """分页管理器 - 智能处理分页逻辑"""
    
    def __init__(self, config: WorkflowConfig):
        """
        初始化分页管理器
        
        Args:
            config: 工作流配置
        """
        super().__init__(config, "pagination")
        
        # 分页配置
        self.pagination_config = config.pagination
        self.pagination_selectors = self.scraper_config.get('pagination', {})
        
        self.logger.info(f"📄 分页管理器初始化: 最大页数={self.pagination_config.max_pages_per_category}")
    
    def scrape(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        实现基类的抽象方法
        
        Args:
            url: 目标URL
            **kwargs: 额外参数
            
        Returns:
            分页信息
        """
        return self.detect_pagination_info(url)
    
    def detect_max_pages(self, category_url: str) -> int:
        """
        自动检测分类的最大页数
        
        Args:
            category_url: 分类URL
            
        Returns:
            最大页数
        """
        if not self.pagination_config.auto_detect_max_pages:
            return self.pagination_config.max_pages_per_category
        
        self.logger.info(f"🔍 检测最大页数: {category_url}")
        
        try:
            # 方法1: 尝试从第一页检测分页信息
            pagination_info = self.detect_pagination_info(category_url)
            if pagination_info.get('total_pages', 0) > 0:
                detected_pages = pagination_info['total_pages']
                self.logger.info(f"📄 检测到最大页数: {detected_pages}")
                return detected_pages
            
            # 方法2: 二分查找法检测最大页数
            max_pages = self._binary_search_max_pages(category_url)
            if max_pages > 0:
                self.logger.info(f"📄 通过二分查找检测到最大页数: {max_pages}")
                return max_pages
            
            # 方法3: 使用配置的默认值
            default_pages = self.pagination_config.max_pages_per_category
            self.logger.warning(f"⚠️  无法检测最大页数，使用默认值: {default_pages}")
            return default_pages
            
        except Exception as e:
            self.logger.error(f"❌ 页数检测失败: {e}")
            return self.pagination_config.max_pages_per_category
    
    def detect_pagination_info(self, url: str) -> Dict[str, Any]:
        """
        检测分页信息
        
        Args:
            url: 页面URL
            
        Returns:
            分页信息
        """
        self.logger.debug(f"🔍 检测分页信息: {url}")
        
        try:
            # 构建分页检测的解析指令
            parsing_instructions = self._get_pagination_parsing_instructions()
            
            # 发送请求
            response = self.make_request(
                url=url,
                parsing_instructions=parsing_instructions,
                strategy="fast"  # 使用快速策略
            )
            
            if not response or not self.validate_response(response):
                self.logger.warning("⚠️  分页信息检测请求失败")
                return {}
            
            # 提取分页数据
            pagination_data = self.extract_data_from_response(response)
            
            # 处理分页信息
            return self._process_pagination_data(pagination_data, url)
            
        except Exception as e:
            self.logger.error(f"❌ 分页信息检测失败: {e}")
            return {}
    
    def build_page_url(self, base_url: str, page_number: int) -> str:
        """
        构建指定页码的URL
        
        Args:
            base_url: 基础URL
            page_number: 页码
            
        Returns:
            页面URL
        """
        if page_number <= 1:
            return base_url
        
        # 解析URL
        parsed = urlparse(base_url)
        query_params = parse_qs(parsed.query)
        
        # 获取分页参数配置
        page_param = self.pagination_selectors.get('url_patterns', {}).get('page_param', 'page')
        
        # 设置页码参数
        query_params[page_param] = [str(page_number)]
        
        # 重新构建URL
        new_query = urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        
        page_url = urlunparse(new_parsed)
        self.logger.debug(f"📄 构建页面URL: 第{page_number}页 -> {page_url}")
        
        return page_url
    
    def calculate_effective_pages(self, actual_pages: int, config_max: int) -> int:
        """
        计算有效页数（根据限制策略）
        
        Args:
            actual_pages: 实际页数
            config_max: 配置的最大页数
            
        Returns:
            有效页数
        """
        strategy = self.pagination_config.limit_strategy
        
        if strategy == "min":
            effective_pages = min(actual_pages, config_max)
        elif strategy == "config_only":
            effective_pages = config_max
        elif strategy == "actual_only":
            effective_pages = actual_pages
        else:
            effective_pages = min(actual_pages, config_max)
        
        self.logger.debug(f"📊 页数计算: 实际={actual_pages}, 配置={config_max}, "
                         f"策略={strategy}, 有效={effective_pages}")
        
        return max(1, effective_pages)  # 至少1页
    
    def _get_pagination_parsing_instructions(self) -> Dict[str, Any]:
        """获取分页检测的解析指令"""
        return {
            "pagination_info": {
                "current_page": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//nav[@data-testid='pagination']//button[@aria-current='page']/text()"
                        }
                    ]
                },
                "total_pages": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//nav[@data-testid='pagination']//button[last()]/text()"
                        }
                    ]
                },
                "next_page_exists": {
                    "_fns": [
                        {
                            "_fn": "xpath_one",
                            "_args": "//a[@aria-label='Next Page']/@href"
                        }
                    ]
                },
                "page_numbers": {
                    "_fns": [
                        {
                            "_fn": "xpath",
                            "_args": "//nav[@data-testid='pagination']//button/text()"
                        }
                    ]
                }
            }
        }
    
    def _process_pagination_data(self, pagination_data: Dict[str, Any], url: str) -> Dict[str, Any]:
        """
        处理分页数据
        
        Args:
            pagination_data: 原始分页数据
            url: 当前URL
            
        Returns:
            处理后的分页信息
        """
        pagination_info = pagination_data.get('pagination_info', {})
        
        # 提取当前页
        current_page = self._extract_page_number(pagination_info.get('current_page', '1'))
        
        # 提取总页数
        total_pages = self._extract_total_pages(pagination_info)
        
        # 检查是否有下一页
        has_next = bool(pagination_info.get('next_page_exists', ''))
        
        result = {
            "current_page": current_page,
            "total_pages": total_pages,
            "has_next_page": has_next,
            "url": url,
            "detected_at": time.time()
        }
        
        self.logger.debug(f"📄 分页信息: 当前页={current_page}, 总页数={total_pages}, 有下一页={has_next}")
        
        return result
    
    def _extract_page_number(self, page_text: str) -> int:
        """从文本中提取页码"""
        if not page_text:
            return 1
        
        try:
            # 提取数字
            page_match = re.search(r'\d+', str(page_text))
            if page_match:
                return int(page_match.group())
        except (ValueError, AttributeError):
            pass
        
        return 1
    
    def _extract_total_pages(self, pagination_info: Dict[str, Any]) -> int:
        """提取总页数"""
        # 方法1: 直接从total_pages字段获取
        total_pages_text = pagination_info.get('total_pages', '')
        if total_pages_text:
            try:
                total_pages = self._extract_page_number(total_pages_text)
                if total_pages > 0:
                    return total_pages
            except:
                pass
        
        # 方法2: 从页码列表中获取最大值
        page_numbers = pagination_info.get('page_numbers', [])
        if page_numbers:
            try:
                max_page = 0
                for page_text in page_numbers:
                    page_num = self._extract_page_number(page_text)
                    max_page = max(max_page, page_num)
                
                if max_page > 0:
                    return max_page
            except:
                pass
        
        return 0  # 无法确定总页数
    
    def _binary_search_max_pages(self, category_url: str, max_search: int = 100) -> int:
        """
        使用二分查找法检测最大页数
        
        Args:
            category_url: 分类URL
            max_search: 最大搜索页数
            
        Returns:
            最大页数
        """
        self.logger.debug(f"🔍 开始二分查找最大页数: {category_url}")
        
        left, right = 1, max_search
        last_valid_page = 1
        
        timeout = self.pagination_config.page_detection_timeout
        start_time = time.time()
        
        while left <= right and (time.time() - start_time) < timeout:
            mid = (left + right) // 2
            
            self.logger.debug(f"🔍 测试页码: {mid}")
            
            # 构建测试页面URL
            test_url = self.build_page_url(category_url, mid)
            
            # 检查页面是否存在
            if self._page_exists(test_url):
                last_valid_page = mid
                left = mid + 1
                self.logger.debug(f"✅ 页码 {mid} 存在")
            else:
                right = mid - 1
                self.logger.debug(f"❌ 页码 {mid} 不存在")
            
            # 添加小延迟避免请求过快
            time.sleep(0.5)
        
        self.logger.debug(f"🔍 二分查找完成: 最大页数={last_valid_page}")
        return last_valid_page
    
    def _page_exists(self, page_url: str) -> bool:
        """
        检查页面是否存在（有有效内容）
        
        Args:
            page_url: 页面URL
            
        Returns:
            页面是否存在
        """
        try:
            # 发送简单请求检查页面
            response = self.make_request(
                url=page_url,
                strategy="fast"
            )
            
            if not response or not self.validate_response(response):
                return False
            
            # 检查是否有产品内容
            content = self.extract_data_from_response(response)
            
            # 简单检查：如果有HTML内容且不是错误页面
            if isinstance(content, dict) and content.get('html'):
                html = content['html']
                # 检查是否包含产品相关内容
                if any(keyword in html.lower() for keyword in ['product', 'item', 'data-testid']):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"⚠️  页面检查失败: {e}")
            return False
    
    def get_page_range(self, start_page: int, end_page: int, base_url: str) -> List[str]:
        """
        获取页面范围的URL列表
        
        Args:
            start_page: 起始页码
            end_page: 结束页码
            base_url: 基础URL
            
        Returns:
            页面URL列表
        """
        urls = []
        for page_num in range(start_page, end_page + 1):
            url = self.build_page_url(base_url, page_num)
            urls.append(url)
        
        self.logger.debug(f"📄 生成页面范围: {start_page}-{end_page}, 共{len(urls)}个URL")
        return urls


# 便捷函数
def create_pagination_manager(config: WorkflowConfig) -> PaginationManager:
    """创建分页管理器的便捷函数"""
    return PaginationManager(config)
