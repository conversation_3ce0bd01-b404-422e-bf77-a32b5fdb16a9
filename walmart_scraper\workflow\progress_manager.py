#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度管理器
支持工作流中断后的进度保存和恢复功能
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

from .workflow_config import WorkflowConfig
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.logger import get_logger


@dataclass
class WorkflowProgress:
    """工作流进度状态"""
    workflow_id: str
    start_time: float
    current_step: str
    total_categories: int
    completed_categories: int
    current_category: Optional[str]
    current_page: int
    total_pages_current_category: int
    categories_status: Dict[str, str]  # category_name -> status (pending, processing, completed, failed)
    completed_products: int
    last_checkpoint: float
    config_snapshot: Dict[str, Any]
    errors: List[str]


@dataclass
class CategoryProgress:
    """单个分类的进度状态"""
    category_name: str
    category_url: str
    status: str  # pending, processing, completed, failed
    current_page: int
    total_pages: int
    completed_products: int
    start_time: float
    end_time: Optional[float]
    errors: List[str]


class ProgressManager:
    """进度管理器"""
    
    def __init__(self, config: WorkflowConfig):
        """
        初始化进度管理器
        
        Args:
            config: 工作流配置
        """
        self.config = config
        self.logger = get_logger("walmart_scraper.progress")
        
        # 进度文件路径
        self.progress_file = os.path.join(
            config.output.base_dir,
            config.progress.progress_file
        )
        self.backup_file = f"{self.progress_file}.backup"
        
        # 当前进度状态
        self.current_progress: Optional[WorkflowProgress] = None
        self.categories_progress: Dict[str, CategoryProgress] = {}
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
    
    def initialize_workflow_progress(self, workflow_id: str, 
                                   categories: List[Dict[str, Any]]) -> WorkflowProgress:
        """
        初始化工作流进度
        
        Args:
            workflow_id: 工作流ID
            categories: 分类列表
            
        Returns:
            工作流进度对象
        """
        self.logger.info(f"📋 初始化工作流进度: {workflow_id}")
        
        # 创建分类状态映射
        categories_status = {cat['name']: 'pending' for cat in categories}
        
        # 创建进度对象
        self.current_progress = WorkflowProgress(
            workflow_id=workflow_id,
            start_time=time.time(),
            current_step="INIT",
            total_categories=len(categories),
            completed_categories=0,
            current_category=None,
            current_page=0,
            total_pages_current_category=0,
            categories_status=categories_status,
            completed_products=0,
            last_checkpoint=time.time(),
            config_snapshot=self.config.to_dict(),
            errors=[]
        )
        
        # 初始化分类进度
        for category in categories:
            self.categories_progress[category['name']] = CategoryProgress(
                category_name=category['name'],
                category_url=category.get('full_url', ''),
                status='pending',
                current_page=0,
                total_pages=0,
                completed_products=0,
                start_time=0,
                end_time=None,
                errors=[]
            )
        
        # 保存初始进度
        self.save_progress()
        
        return self.current_progress
    
    def update_workflow_step(self, step: str):
        """
        更新工作流步骤
        
        Args:
            step: 当前步骤名称
        """
        if self.current_progress:
            self.current_progress.current_step = step
            self.logger.debug(f"🔄 工作流步骤更新: {step}")
    
    def start_category_processing(self, category_name: str, total_pages: int):
        """
        开始处理分类
        
        Args:
            category_name: 分类名称
            total_pages: 总页数
        """
        self.logger.info(f"📂 开始处理分类: {category_name} ({total_pages} 页)")
        
        if self.current_progress:
            self.current_progress.current_category = category_name
            self.current_progress.current_page = 0
            self.current_progress.total_pages_current_category = total_pages
            self.current_progress.categories_status[category_name] = 'processing'
        
        if category_name in self.categories_progress:
            category_progress = self.categories_progress[category_name]
            category_progress.status = 'processing'
            category_progress.total_pages = total_pages
            category_progress.start_time = time.time()
        
        self.save_progress()
    
    def update_page_progress(self, category_name: str, page_num: int, 
                           products_count: int):
        """
        更新页面处理进度
        
        Args:
            category_name: 分类名称
            page_num: 当前页码
            products_count: 当前页产品数量
        """
        self.logger.debug(f"📄 页面进度更新: {category_name} 第{page_num}页, {products_count}个产品")
        
        if self.current_progress:
            self.current_progress.current_page = page_num
            self.current_progress.completed_products += products_count
        
        if category_name in self.categories_progress:
            category_progress = self.categories_progress[category_name]
            category_progress.current_page = page_num
            category_progress.completed_products += products_count
        
        # 检查是否需要保存检查点
        self._check_and_save_checkpoint()
    
    def complete_category_processing(self, category_name: str, success: bool = True,
                                   error_msg: str = None):
        """
        完成分类处理
        
        Args:
            category_name: 分类名称
            success: 是否成功
            error_msg: 错误信息（如果失败）
        """
        status = 'completed' if success else 'failed'
        self.logger.info(f"✅ 分类处理完成: {category_name} - {status}")
        
        if self.current_progress:
            self.current_progress.completed_categories += 1
            self.current_progress.categories_status[category_name] = status
            self.current_progress.current_category = None
            
            if error_msg:
                self.current_progress.errors.append(f"{category_name}: {error_msg}")
        
        if category_name in self.categories_progress:
            category_progress = self.categories_progress[category_name]
            category_progress.status = status
            category_progress.end_time = time.time()
            
            if error_msg:
                category_progress.errors.append(error_msg)
        
        self.save_progress()

    def save_category_progress(self, category_name: str, category_result: Any):
        """
        保存分类处理进度

        Args:
            category_name: 分类名称
            category_result: 分类处理结果
        """
        if not self.config.progress.enabled or not self.current_progress:
            return

        # 查找对应的分类进度
        category_progress = None
        for cp in self.current_progress.categories:
            if cp.category_name == category_name:
                category_progress = cp
                break

        if category_progress:
            # 更新分类进度状态
            if hasattr(category_result, 'success') and category_result.success:
                category_progress.status = "completed"
                category_progress.completed_pages = getattr(category_result, 'total_pages', 0)
                category_progress.total_products = getattr(category_result, 'total_products', 0)
            else:
                category_progress.status = "failed"
                if hasattr(category_result, 'error'):
                    category_progress.errors.append(str(category_result.error))

            category_progress.end_time = datetime.now().isoformat()
            self.save_progress()

    def save_progress(self):
        """保存当前进度到文件"""
        if not self.config.progress.enabled or not self.current_progress:
            return
        
        try:
            # 准备保存数据
            progress_data = {
                "workflow_progress": asdict(self.current_progress),
                "categories_progress": {
                    name: asdict(progress) 
                    for name, progress in self.categories_progress.items()
                },
                "saved_at": datetime.now().isoformat(),
                "version": "1.0.0"
            }
            
            # 备份现有文件
            if self.config.progress.backup_progress and os.path.exists(self.progress_file):
                try:
                    import shutil
                    shutil.copy2(self.progress_file, self.backup_file)
                except Exception as e:
                    self.logger.warning(f"⚠️  进度文件备份失败: {e}")
            
            # 保存进度文件
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"💾 进度已保存: {self.progress_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 进度保存失败: {e}")
    
    def load_progress(self) -> Optional[WorkflowProgress]:
        """
        加载已保存的进度
        
        Returns:
            工作流进度对象，如果没有可用进度则返回None
        """
        if not self.config.progress.enabled:
            return None
        
        try:
            if not os.path.exists(self.progress_file):
                self.logger.debug("📂 未找到进度文件")
                return None
            
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            # 恢复工作流进度
            workflow_data = progress_data.get('workflow_progress', {})
            self.current_progress = WorkflowProgress(**workflow_data)
            
            # 恢复分类进度
            categories_data = progress_data.get('categories_progress', {})
            self.categories_progress = {
                name: CategoryProgress(**data)
                for name, data in categories_data.items()
            }
            
            self.logger.info(f"📂 进度加载成功: 工作流 {self.current_progress.workflow_id}")
            return self.current_progress
            
        except Exception as e:
            self.logger.error(f"❌ 进度加载失败: {e}")
            return None
    
    def can_resume(self) -> bool:
        """
        检查是否可以恢复进度
        
        Returns:
            是否可以恢复
        """
        if not self.config.progress.enabled:
            return False
        
        progress = self.load_progress()
        if not progress:
            return False
        
        # 检查进度是否完整
        incomplete_categories = [
            name for name, status in progress.categories_status.items()
            if status in ['pending', 'processing']
        ]
        
        return len(incomplete_categories) > 0
    
    def get_resume_info(self) -> Optional[Dict[str, Any]]:
        """
        获取恢复信息
        
        Returns:
            恢复信息字典
        """
        progress = self.load_progress()
        if not progress:
            return None
        
        incomplete_categories = [
            name for name, status in progress.categories_status.items()
            if status in ['pending', 'processing']
        ]
        
        completed_categories = [
            name for name, status in progress.categories_status.items()
            if status == 'completed'
        ]
        
        return {
            "workflow_id": progress.workflow_id,
            "start_time": datetime.fromtimestamp(progress.start_time).isoformat(),
            "current_step": progress.current_step,
            "total_categories": progress.total_categories,
            "completed_categories": len(completed_categories),
            "incomplete_categories": incomplete_categories,
            "completed_products": progress.completed_products,
            "can_resume": len(incomplete_categories) > 0
        }
    
    def cleanup_progress(self):
        """清理进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                self.logger.info("🧹 进度文件已清理")
            
            if os.path.exists(self.backup_file):
                os.remove(self.backup_file)
                self.logger.debug("🧹 备份文件已清理")
                
        except Exception as e:
            self.logger.warning(f"⚠️  进度文件清理失败: {e}")
    
    def _check_and_save_checkpoint(self):
        """检查并保存检查点"""
        if not self.current_progress:
            return
        
        current_time = time.time()
        time_since_checkpoint = current_time - self.current_progress.last_checkpoint
        
        # 根据产品数量或时间间隔保存检查点
        products_since_checkpoint = (
            self.current_progress.completed_products % 
            self.config.progress.checkpoint_frequency
        )
        
        if (products_since_checkpoint == 0 and 
            self.current_progress.completed_products > 0) or time_since_checkpoint > 300:  # 5分钟
            
            self.current_progress.last_checkpoint = current_time
            self.save_progress()
            self.logger.debug(f"📍 检查点已保存: {self.current_progress.completed_products} 个产品")


# 便捷函数
def create_progress_manager(config: WorkflowConfig) -> ProgressManager:
    """创建进度管理器的便捷函数"""
    return ProgressManager(config)
