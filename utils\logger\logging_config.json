{"_description": "日志配置文件 - 控制整个项目的日志输出行为", "_version": "1.0.0", "_last_updated": "2025-01-04", "version": 1, "disable_existing_loggers": false, "formatters": {"_description": "日志格式化器配置", "detailed": {"format": "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "_detailed_description": "详细格式：包含时间、模块名、级别、文件位置和消息", "simple": {"format": "%(asctime)s - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "_simple_description": "简单格式：包含时间、级别和消息", "workflow": {"format": "%(asctime)s - [%(workflow_step)s] - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "_workflow_description": "工作流格式：包含工作流步骤信息", "console": {"format": "🕐 %(asctime)s | %(levelname)s | %(message)s", "datefmt": "%H:%M:%S"}, "_console_description": "控制台格式：简洁的用户友好格式"}, "handlers": {"_description": "日志处理器配置", "console": {"class": "logging.StreamHandler", "level": "DEBUG", "formatter": "console", "stream": "ext://sys.stdout"}, "_console_description": "控制台输出处理器，显示INFO及以上级别的日志", "file_debug": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "output/logs/debug.log", "maxBytes": 10485760, "backupCount": 5, "encoding": "utf-8"}, "_file_debug_description": "调试日志文件，记录所有级别的详细日志，10MB轮转", "file_info": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "detailed", "filename": "output/logs/info.log", "maxBytes": 5242880, "backupCount": 3, "encoding": "utf-8"}, "_file_info_description": "信息日志文件，记录INFO及以上级别的日志，5MB轮转", "file_error": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "detailed", "filename": "output/logs/error.log", "maxBytes": 5242880, "backupCount": 5, "encoding": "utf-8"}, "_file_error_description": "错误日志文件，仅记录ERROR及以上级别的日志", "workflow_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "detailed", "filename": "output/logs/workflow.log", "maxBytes": 10485760, "backupCount": 3, "encoding": "utf-8"}, "_workflow_file_description": "工作流专用日志文件，记录工作流执行过程"}, "loggers": {"_description": "具体模块的日志配置", "walmart_scraper": {"level": "DEBUG", "handlers": ["console", "file_debug", "file_info", "file_error"], "propagate": false}, "_walmart_scraper_description": "主模块日志配置", "walmart_scraper.workflow": {"level": "INFO", "handlers": ["console", "workflow_file", "file_info"], "propagate": false}, "_workflow_description": "工作流模块专用日志配置", "walmart_scraper.scrapers": {"level": "DEBUG", "handlers": ["console", "file_debug", "file_error"], "propagate": false}, "_scrapers_description": "抓取器模块日志配置", "walmart_scraper.utils": {"level": "INFO", "handlers": ["file_debug", "file_error"], "propagate": false}, "_utils_description": "工具模块日志配置", "requests": {"level": "WARNING", "handlers": ["file_debug"], "propagate": false}, "_requests_description": "requests库日志配置，只记录警告和错误"}, "root": {"level": "INFO", "handlers": ["console", "file_info"]}, "_root_description": "根日志配置", "custom_settings": {"_description": "自定义日志设置", "log_directory": "output/logs", "_log_directory_description": "日志文件存储目录", "auto_create_dirs": true, "_auto_create_dirs_description": "是否自动创建日志目录", "log_levels": {"_description": "日志级别说明", "DEBUG": "详细的调试信息，包含变量值、函数调用等", "INFO": "一般信息，工作流进度、成功操作等", "WARNING": "警告信息，非致命错误、重试操作等", "ERROR": "错误信息，操作失败、异常等", "CRITICAL": "严重错误，系统无法继续运行"}, "workflow_steps": {"_description": "工作流步骤标识，用于workflow格式化器", "INIT": "初始化", "CATEGORY_DISCOVERY": "分类发现", "CATEGORY_PROCESSING": "分类处理", "PRODUCT_LIST": "产品列表抓取", "PAGINATION": "分页处理", "PRODUCT_DETAIL": "产品详情抓取", "OUTPUT": "结果输出", "PROGRESS": "进度管理", "CLEANUP": "清理工作"}, "performance_logging": {"_description": "性能日志配置", "enabled": true, "log_request_time": true, "log_memory_usage": false, "log_processing_time": true}, "_performance_logging_description": "是否记录性能相关信息", "sensitive_data": {"_description": "敏感数据处理配置", "mask_credentials": true, "mask_personal_info": true, "log_full_urls": false}, "_sensitive_data_description": "敏感数据在日志中的处理方式"}}