#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作流配置管理器
负责加载和管理工作流配置
"""

import os
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class SiteConfig:
    """站点配置"""
    name: str
    display_name: str
    description: str = ""


@dataclass
class CategoryItem:
    """单个分类项"""
    name: str
    url: str
    description: str = ""
    enabled: bool = True

@dataclass
class CategoryConfig:
    """分类配置"""
    categories: List[CategoryItem]


@dataclass
class PaginationConfig:
    """分页配置"""
    max_pages_per_category: int
    max_products_per_page: int
    limit_strategy: str
    auto_detect_max_pages: bool
    page_detection_timeout: int


@dataclass
class SortingInteractionConfig:
    """排序交互配置"""
    sort_button_selector: str
    sort_options_container: str
    wait_timeout: int
    network_idle_timeout: int


@dataclass
class SortingConfig:
    """排序配置"""
    enabled: bool
    method: str
    options: Dict[str, Dict[str, str]]
    default_sort: str
    interaction_config: SortingInteractionConfig
    filters: List[Dict[str, str]]


@dataclass
class ProductDetailConfig:
    """产品详情配置"""
    enabled: bool
    mode: str
    sample_size: int
    sample_strategy: str
    detail_fields: List[str]
    batch_size: int
    delay_between_details: int


@dataclass
class OutputConfig:
    """输出配置"""
    organization: str
    base_dir: str
    save_intermediate: bool
    file_naming: Dict[str, str]
    include_timestamp: bool
    compress_output: bool
    workflow_results_subdir: str = "workflow_results"
    category_results_subdir: str = ""


@dataclass
class ProgressConfig:
    """进度配置"""
    enabled: bool
    checkpoint_frequency: int
    progress_file: str
    auto_cleanup: bool
    backup_progress: bool
    max_resume_attempts: int


@dataclass
class PerformanceConfig:
    """性能配置"""
    request_delay: int
    timeout: int
    retry_attempts: int
    retry_delay: int


@dataclass
class WorkflowConfig:
    """完整的工作流配置"""
    site: SiteConfig
    categories: CategoryConfig
    pagination: PaginationConfig
    sorting: SortingConfig
    product_details: ProductDetailConfig
    output: OutputConfig
    progress: ProgressConfig
    performance: PerformanceConfig
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'WorkflowConfig':
        """从字典创建配置对象（适配新的配置架构）"""
        # 导入配置管理器来获取模块配置
        from ..configs.config_manager import config_manager

        # 从模块配置中获取各模块的配置
        category_config = config_manager.get_module_config('category')
        pagination_config = config_manager.get_module_config('pagination')
        product_list_config = config_manager.get_module_config('product_list')
        product_detail_config = config_manager.get_module_config('product_detail')

        # 构建分类配置
        categories_data = category_config

        # 构建分页配置
        pagination_data = pagination_config.get('pagination', {})
        final_pagination_data = {
            'max_pages_per_category': pagination_data.get('max_pages_per_category', pagination_data.get('max_pages', 5)),
            'max_products_per_page': pagination_data.get('max_products_per_page', 50),
            'limit_strategy': pagination_data.get('limit_strategy', 'min'),
            'auto_detect_max_pages': pagination_data.get('auto_detect_max_pages', True),
            'page_detection_timeout': pagination_data.get('page_detection_timeout', 30)
        }

        # 构建排序配置
        sorting_data = product_list_config.get('sorting', {})
        if 'interaction_config' in sorting_data:
            sorting_data['interaction_config'] = SortingInteractionConfig(**sorting_data['interaction_config'])

        # 构建产品详情配置（只提取相关字段）
        product_detail_data = {
            'enabled': product_detail_config.get('enabled', False),
            'mode': product_detail_config.get('mode', 'none'),
            'sample_size': product_detail_config.get('sample_size', 10),
            'sample_strategy': product_detail_config.get('sample_strategy', 'random'),
            'detail_fields': product_detail_config.get('detail_fields', []),
            'batch_size': product_detail_config.get('batch_size', 5),
            'delay_between_details': product_detail_config.get('delay_between_details', 2)
        }

        # 构建站点配置
        site_data = config_dict.get('site', {})
        site_config = SiteConfig(
            name=site_data.get('name', 'unknown'),
            display_name=site_data.get('display_name', 'Unknown Site'),
            description=site_data.get('description', '')
        )

        # 处理输出配置的变量替换
        output_data = config_dict.get('output', {}).copy()
        if 'base_dir' in output_data:
            # 替换变量
            output_data['base_dir'] = output_data['base_dir'].replace('{site_name}', site_config.name)

        # 处理分类配置
        category_items = []
        for cat_data in categories_data.get('categories', []):
            category_items.append(CategoryItem(**cat_data))

        category_config = CategoryConfig(categories=category_items)

        return cls(
            site=site_config,
            categories=category_config,
            pagination=PaginationConfig(**final_pagination_data),
            sorting=SortingConfig(**sorting_data),
            product_details=ProductDetailConfig(**product_detail_data),
            output=OutputConfig(**output_data),
            progress=ProgressConfig(**config_dict.get('progress', {})),
            performance=PerformanceConfig(**config_dict.get('performance', {}))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'site': self.site.__dict__,
            'categories': self.categories.__dict__,
            'pagination': self.pagination.__dict__,
            'sorting': self.sorting.__dict__,
            'product_details': self.product_details.__dict__,
            'output': self.output.__dict__,
            'progress': self.progress.__dict__,
            'performance': self.performance.__dict__
        }


class WorkflowConfigManager:
    """工作流配置管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self._config = None
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        current_dir = Path(__file__).parent.parent
        return str(current_dir / "configs" / "workflow_config.json")
    
    def load_config(self) -> WorkflowConfig:
        """
        加载工作流配置
        
        Returns:
            工作流配置对象
        """
        if self._config is None:
            self._config = self._load_from_file()
        return self._config
    
    def _load_from_file(self) -> WorkflowConfig:
        """从文件加载配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # 过滤注释字段
            filtered_config = self._filter_comments(config_dict)
            
            # 验证配置
            self._validate_config(filtered_config)
            
            return WorkflowConfig.from_dict(filtered_config)
            
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ValueError(f"配置加载失败: {e}")
    
    def _filter_comments(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤配置中的注释字段（以_开头的字段）"""
        if isinstance(config, dict):
            return {
                key: self._filter_comments(value)
                for key, value in config.items()
                if not key.startswith('_')
            }
        elif isinstance(config, list):
            return [self._filter_comments(item) for item in config]
        else:
            return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置的有效性（适配新的配置架构）"""
        # 新架构下只需要验证工作流级别的配置
        required_sections = ['output', 'progress', 'performance']

        for section in required_sections:
            if section not in config:
                raise ValueError(f"缺少必需的配置节: {section}")

        # 验证模块配置是否可用
        from ..configs.config_manager import config_manager

        # 验证分类配置
        category_config = config_manager.get_module_config('category')
        categories = category_config.get('categories', [])
        if not categories:
            raise ValueError("分类模块配置中 categories 不能为空")

        # 验证至少有一个启用的分类
        enabled_categories = [cat for cat in categories if cat.get('enabled', True)]
        if not enabled_categories:
            raise ValueError("分类模块配置中至少需要一个启用的分类")

        # 验证分页配置
        pagination_config = config_manager.get_module_config('pagination')
        pagination_data = pagination_config.get('pagination', {})
        max_pages = pagination_data.get('max_pages_per_category', pagination_data.get('max_pages', 0))
        if max_pages <= 0:
            raise ValueError("分页模块配置中 max_pages 必须大于0")

        valid_limit_strategies = ['min', 'config_only', 'actual_only']
        if pagination_data.get('limit_strategy') not in valid_limit_strategies:
            raise ValueError(f"分页模块配置中 limit_strategy 必须是: {valid_limit_strategies}")

        # 验证排序配置
        product_list_config = config_manager.get_module_config('product_list')
        sorting_config = product_list_config.get('sorting', {})
        if sorting_config.get('enabled'):
            if not sorting_config.get('options'):
                raise ValueError("产品列表模块配置中启用排序时必须提供排序选项")

            # 验证排序方法
            valid_methods = ['url_param', 'browser_interaction']
            if sorting_config.get('method') not in valid_methods:
                raise ValueError(f"产品列表模块配置中 sorting.method 必须是: {valid_methods}")

            # 验证默认排序选项存在
            default_sort = sorting_config.get('default_sort')
            if default_sort and default_sort not in sorting_config.get('options', {}):
                raise ValueError(f"产品列表模块配置中 default_sort '{default_sort}' 不在可用选项中")

            # 验证浏览器交互配置（如果使用browser_interaction方法）
            if sorting_config.get('method') == 'browser_interaction':
                interaction_config = sorting_config.get('interaction_config')
                if not interaction_config:
                    raise ValueError("产品列表模块配置中使用browser_interaction方法时必须提供interaction_config")

                required_interaction_fields = ['sort_button_selector', 'wait_timeout']
                for field in required_interaction_fields:
                    if not interaction_config.get(field):
                        raise ValueError(f"产品列表模块配置中 interaction_config.{field} 不能为空")

        # 验证产品详情配置
        product_detail_config = config_manager.get_module_config('product_detail')
        valid_detail_modes = ['all', 'sample', 'none']
        if product_detail_config.get('mode') not in valid_detail_modes:
            raise ValueError(f"产品详情模块配置中 mode 必须是: {valid_detail_modes}")
        
        # 验证输出配置
        output = config['output']
        valid_organizations = ['by_category', 'unified']
        if output.get('organization') not in valid_organizations:
            raise ValueError(f"output.organization 必须是: {valid_organizations}")
    
    def save_config(self, config: WorkflowConfig):
        """
        保存配置到文件
        
        Args:
            config: 工作流配置对象
        """
        config_dict = config.to_dict()
        
        # 添加元数据
        config_dict['_description'] = "Walmart 工作流配置文件"
        config_dict['_version'] = "1.0.0"
        config_dict['_last_updated'] = "2025-01-04"
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
    
    def get_category_targets(self) -> List[str]:
        """获取目标分类列表"""
        config = self.load_config()
        return [cat.name for cat in config.categories.categories if cat.enabled]
    
    def get_max_pages(self) -> int:
        """获取最大页数配置"""
        config = self.load_config()
        return config.pagination.max_pages_per_category
    
    def get_sorting_options(self) -> Dict[str, Dict[str, str]]:
        """获取排序选项"""
        config = self.load_config()
        return config.sorting.options if config.sorting.enabled else {}
    
    def is_product_details_enabled(self) -> bool:
        """检查是否启用产品详情抓取"""
        config = self.load_config()
        return config.product_details.enabled
    
    def get_output_directory(self) -> str:
        """获取输出目录"""
        config = self.load_config()
        return config.output.base_dir
    
    def is_progress_enabled(self) -> bool:
        """检查是否启用进度恢复"""
        config = self.load_config()
        return config.progress.enabled
    
    def update_config_section(self, section: str, updates: Dict[str, Any]):
        """
        更新配置的某个部分
        
        Args:
            section: 配置节名称
            updates: 更新的配置项
        """
        config = self.load_config()
        
        if hasattr(config, section):
            section_obj = getattr(config, section)
            for key, value in updates.items():
                if hasattr(section_obj, key):
                    setattr(section_obj, key, value)
                else:
                    raise ValueError(f"配置节 {section} 中不存在字段: {key}")
        else:
            raise ValueError(f"不存在配置节: {section}")
        
        self.save_config(config)
        self._config = config  # 更新缓存
    
    def create_custom_config(self, **kwargs) -> WorkflowConfig:
        """
        创建自定义配置

        Args:
            **kwargs: 自定义配置参数

        Returns:
            自定义工作流配置
        """
        base_config = self.load_config()

        # 更新指定的配置项
        for section_name, section_updates in kwargs.items():
            if hasattr(base_config, section_name):
                section_obj = getattr(base_config, section_name)
                for key, value in section_updates.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)

        return base_config

    def load_config_with_overrides(self, user_overrides: Dict[str, Any] = None) -> WorkflowConfig:
        """
        加载配置并应用用户覆盖

        Args:
            user_overrides: 用户覆盖配置，格式: {"max_pages": 3, "enable_details": True}

        Returns:
            应用覆盖后的工作流配置
        """
        # 加载基础配置
        config = self.load_config()

        # 应用用户覆盖
        if user_overrides:
            # 处理最大页数覆盖
            if 'max_pages' in user_overrides:
                config.pagination.max_pages_per_category = user_overrides['max_pages']

            # 处理产品详情覆盖
            if 'enable_details' in user_overrides:
                config.product_details.enabled = user_overrides['enable_details']
                if user_overrides['enable_details']:
                    config.product_details.mode = "sample"
                else:
                    config.product_details.mode = "none"

            # 处理进度恢复覆盖
            if 'resume' in user_overrides:
                config.progress.enabled = user_overrides['resume']

            # 处理自定义URL覆盖
            if 'custom_url' in user_overrides:
                config.categories.discovery_url = user_overrides['custom_url']

        return config


# 便捷函数
def load_workflow_config(config_path: str = None) -> WorkflowConfig:
    """加载工作流配置的便捷函数"""
    manager = WorkflowConfigManager(config_path)
    return manager.load_config()


def get_config_manager(config_path: str = None) -> WorkflowConfigManager:
    """获取配置管理器的便捷函数"""
    return WorkflowConfigManager(config_path)


# 使用示例
if __name__ == "__main__":
    # 加载配置
    config_manager = WorkflowConfigManager()
    config = config_manager.load_config()
    
    print("📋 工作流配置加载成功:")
    print(f"   目标分类: {[cat.name for cat in config.categories.categories if cat.enabled]}")
    print(f"   最大页数: {config.pagination.max_pages_per_category}")
    print(f"   输出目录: {config.output.base_dir}")
    print(f"   进度恢复: {config.progress.enabled}")
    
    # 获取特定配置
    print(f"\n🎯 目标分类: {config_manager.get_category_targets()}")
    print(f"📄 最大页数: {config_manager.get_max_pages()}")
    print(f"📁 输出目录: {config_manager.get_output_directory()}")
